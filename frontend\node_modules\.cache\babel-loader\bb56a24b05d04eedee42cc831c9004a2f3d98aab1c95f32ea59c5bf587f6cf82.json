{"ast": null, "code": "import { quadraticProjectPoint } from '../core/curve.js';\nexport function containStroke(x0, y0, x1, y1, x2, y2, lineWidth, x, y) {\n  if (lineWidth === 0) {\n    return false;\n  }\n  var _l = lineWidth;\n  if (y > y0 + _l && y > y1 + _l && y > y2 + _l || y < y0 - _l && y < y1 - _l && y < y2 - _l || x > x0 + _l && x > x1 + _l && x > x2 + _l || x < x0 - _l && x < x1 - _l && x < x2 - _l) {\n    return false;\n  }\n  var d = quadraticProjectPoint(x0, y0, x1, y1, x2, y2, x, y, null);\n  return d <= _l / 2;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}