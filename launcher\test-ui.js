/**
 * UI功能完整测试
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🎨 启动UI功能测试...\n');

// 检查所有页面文件是否存在
const pageFiles = [
    'src/renderer/index.html',
    'src/renderer/config.html', 
    'src/renderer/status.html',
    'src/renderer/help.html',
    'src/renderer/js/config.js',
    'src/renderer/js/status.js'
];

console.log('📁 检查页面文件:');
let allPagesExist = true;

pageFiles.forEach(file => {
    const filePath = path.join(__dirname, file);
    if (fs.existsSync(filePath)) {
        console.log(`✅ ${file}`);
    } else {
        console.log(`❌ ${file} - 文件不存在`);
        allPagesExist = false;
    }
});

if (!allPagesExist) {
    console.log('\n❌ 部分页面文件缺失，请检查文件结构');
    process.exit(1);
}

console.log('\n✅ 所有页面文件检查通过');

// 检查JavaScript语法
console.log('\n🔍 检查JavaScript语法:');

const jsFiles = [
    'src/renderer/js/config.js',
    'src/renderer/js/status.js'
];

jsFiles.forEach(file => {
    try {
        const filePath = path.join(__dirname, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // 简单的语法检查
        if (content.includes('class ') && content.includes('constructor(') && content.includes('addEventListener(')) {
            console.log(`✅ ${file} - 语法结构正确`);
        } else {
            console.log(`⚠️ ${file} - 可能存在语法问题`);
        }
    } catch (error) {
        console.log(`❌ ${file} - 读取失败: ${error.message}`);
    }
});

// 检查HTML结构
console.log('\n🏗️ 检查HTML结构:');

const htmlFiles = [
    { file: 'src/renderer/index.html', requiredElements: ['config-btn', 'start-btn', 'status-btn', 'help-btn'] },
    { file: 'src/renderer/config.html', requiredElements: ['tushare-token', 'test-tushare', 'save-btn'] },
    { file: 'src/renderer/status.html', requiredElements: ['backend-status', 'frontend-status', 'start-btn'] },
    { file: 'src/renderer/help.html', requiredElements: ['back-btn', 'app-version'] }
];

htmlFiles.forEach(({ file, requiredElements }) => {
    try {
        const filePath = path.join(__dirname, file);
        const content = fs.readFileSync(filePath, 'utf8');
        
        let allElementsFound = true;
        requiredElements.forEach(elementId => {
            if (!content.includes(`id="${elementId}"`)) {
                console.log(`⚠️ ${file} - 缺少元素: ${elementId}`);
                allElementsFound = false;
            }
        });
        
        if (allElementsFound) {
            console.log(`✅ ${file} - 所有必需元素都存在`);
        }
    } catch (error) {
        console.log(`❌ ${file} - 检查失败: ${error.message}`);
    }
});

// 启动Electron应用进行实际测试
console.log('\n🚀 启动Electron应用进行UI测试...');

const electronProcess = spawn('npx', ['electron', '.', '--dev'], {
    cwd: __dirname,
    stdio: 'pipe'
});

let output = '';

electronProcess.stdout.on('data', (data) => {
    output += data.toString();
});

electronProcess.stderr.on('data', (data) => {
    const error = data.toString();
    if (!error.includes('libpng warning')) { // 忽略libpng警告
        console.log('⚠️ Electron错误:', error);
    }
});

electronProcess.on('error', (error) => {
    console.log('❌ Electron启动失败:', error.message);
});

// 15秒后检查结果
setTimeout(() => {
    console.log('\n📊 测试结果:');
    
    if (electronProcess.pid) {
        console.log('✅ Electron应用启动成功');
        console.log(`📍 进程ID: ${electronProcess.pid}`);
        
        // 检查是否有严重错误
        if (output.includes('Error') || output.includes('Failed')) {
            console.log('⚠️ 检测到可能的错误，请查看详细日志');
        } else {
            console.log('✅ 未检测到严重错误');
        }
        
        console.log('\n🎉 UI测试完成！');
        console.log('💡 应用应该已经在屏幕上显示，请手动测试以下功能:');
        console.log('   1. 主页面四个按钮是否可点击');
        console.log('   2. 配置页面是否能正常导航和输入');
        console.log('   3. 状态页面是否能显示服务状态');
        console.log('   4. 帮助页面是否能正常显示');
        console.log('   5. 页面间导航是否正常');
        
        // 10秒后自动关闭
        setTimeout(() => {
            console.log('\n⏰ 自动关闭应用...');
            electronProcess.kill();
            process.exit(0);
        }, 10000);
        
    } else {
        console.log('❌ Electron应用启动失败');
        process.exit(1);
    }
}, 15000);

// 处理进程退出
electronProcess.on('exit', (code) => {
    if (code === 0) {
        console.log('\n✅ Electron应用正常退出');
    } else {
        console.log(`\n⚠️ Electron应用异常退出，代码: ${code}`);
    }
});

// 处理Ctrl+C
process.on('SIGINT', () => {
    console.log('\n🛑 收到中断信号，正在关闭...');
    if (electronProcess.pid) {
        electronProcess.kill();
    }
    process.exit(0);
});
