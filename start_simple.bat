@echo off
echo ========================================
echo Quantitative Trading System Startup
echo ========================================
echo.

:: Check Python
echo [1/4] Checking Python environment...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    echo Please install Python 3.8+ and add to PATH
    pause
    exit /b 1
)
echo OK: Python environment ready

:: Check Node.js
echo [2/4] Checking Node.js environment...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    echo Please install Node.js 16+ and add to PATH
    pause
    exit /b 1
)
echo OK: Node.js environment ready

:: Get current directory
set "SCRIPT_DIR=%~dp0"
echo Script directory: %SCRIPT_DIR%

:: Install backend dependencies
echo [3/4] Installing backend dependencies...
cd /d "%SCRIPT_DIR%backend"
echo Current directory: %CD%
if not exist "venv" (
    echo Creating Python virtual environment...
    python -m venv venv
)
call venv\Scripts\activate.bat
pip install fastapi uvicorn pandas numpy tushare requests
if %errorlevel% neq 0 (
    echo ERROR: Backend dependencies installation failed
    pause
    exit /b 1
)
echo OK: Backend dependencies installed

:: Install frontend dependencies
echo [4/4] Installing frontend dependencies...
cd /d "%SCRIPT_DIR%frontend"
echo Current directory: %CD%
if not exist "node_modules" (
    echo Installing frontend packages...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Frontend dependencies installation failed
        pause
        exit /b 1
    )
)
echo OK: Frontend dependencies installed

:: Return to script directory
cd /d "%SCRIPT_DIR%"

:: Create necessary directories
if not exist "%SCRIPT_DIR%backend\logs" mkdir "%SCRIPT_DIR%backend\logs"
if not exist "%SCRIPT_DIR%data" mkdir "%SCRIPT_DIR%data"

:: Start services
echo.
echo Starting Quantitative Trading System...
echo.
echo Backend API: http://localhost:8000
echo Frontend UI: http://localhost:3000
echo API Docs: http://localhost:8000/docs
echo.

:: Start backend service
echo Starting backend service...
start "Backend-API" cmd /k "cd /d \"%SCRIPT_DIR%backend\" && venv\Scripts\activate.bat && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

:: Wait for backend to start
echo Waiting for backend to start...
timeout /t 5 /nobreak >nul

:: Start frontend service
echo Starting frontend service...
start "Frontend-UI" cmd /k "cd /d \"%SCRIPT_DIR%frontend\" && set PORT=3001 && npm start"

echo.
echo ========================================
echo System startup completed!
echo ========================================
echo.
echo Usage:
echo 1. Backend API: http://localhost:8000
echo 2. Frontend UI: http://localhost:3001
echo 3. API Documentation: http://localhost:8000/docs
echo 4. Health Check: http://localhost:8000/health
echo.
echo To stop services, close the command windows
echo.
pause
