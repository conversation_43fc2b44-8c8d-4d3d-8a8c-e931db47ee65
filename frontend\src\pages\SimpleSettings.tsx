import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  message,
  Space,
  Alert,
  Typography,
  Divider
} from 'antd';
import {
  SaveOutlined,
  CheckCircleOutlined,
  WarningOutlined
} from '@ant-design/icons';

const { Title, Text } = Typography;
const { TextArea } = Input;

interface TokenConfig {
  tushare_token: string;
  deepseek_api_key: string;
}

const SimpleSettings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const validateTushareToken = (token: string): boolean => {
    return token.length >= 32;
  };

  const validateDeepSeekKey = (key: string): boolean => {
    return key.startsWith('sk-') && key.length > 10;
  };

  const handleSave = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 验证Token格式
      if (values.tushare_token && !validateTushareToken(values.tushare_token)) {
        message.error('Tushare Token格式不正确，应至少32位');
        return;
      }

      if (values.deepseek_api_key && !validateDeepSeekKey(values.deepseek_api_key)) {
        message.error('DeepSeek API Key格式不正确，应以sk-开头');
        return;
      }

      // 保存到本地存储
      localStorage.setItem('quantTradingConfig', JSON.stringify(values));
      
      // 同时保存到配置文件（通过简单的方式）
      const configText = JSON.stringify({
        tushare_token: values.tushare_token || '',
        deepseek_api_key: values.deepseek_api_key || '***********************************',
        enabled_features: {
          ai_analysis: true,
          realtime_data: true,
          notifications: true
        }
      }, null, 2);

      message.success('配置已保存到本地存储');
      message.info('请将以下配置复制到 backend/config/user_config.json 文件中：');
      
      // 显示配置内容供用户复制
      console.log('配置内容：', configText);
      
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      setLoading(false);
    }
  };

  const loadFromLocal = () => {
    try {
      const saved = localStorage.getItem('quantTradingConfig');
      if (saved) {
        const config = JSON.parse(saved);
        form.setFieldsValue(config);
        message.success('已加载本地配置');
      } else {
        message.info('未找到本地配置');
      }
    } catch (error) {
      message.error('加载本地配置失败');
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '800px', margin: '0 auto' }}>
      <Title level={2}>🔑 Token配置</Title>
      
      <Alert
        message="简化配置界面"
        description="这是一个简化的配置界面，配置将保存到浏览器本地存储。"
        type="info"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <Card>
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Form.Item
            label="Tushare Token"
            name="tushare_token"
            rules={[
              { required: true, message: '请输入Tushare Token' },
              { min: 32, message: 'Token长度应至少32位' }
            ]}
          >
            <Input.Password
              placeholder="请输入您的Tushare Pro Token"
              size="large"
            />
          </Form.Item>

          <Form.Item
            label="DeepSeek API Key"
            name="deepseek_api_key"
            rules={[
              { pattern: /^sk-/, message: 'API Key应以sk-开头' }
            ]}
          >
            <Input.Password
              placeholder="请输入您的DeepSeek API Key（可选）"
              size="large"
            />
          </Form.Item>

          <Divider />

          <Space size="large">
            <Button
              type="primary"
              icon={<SaveOutlined />}
              onClick={handleSave}
              loading={loading}
              size="large"
            >
              保存配置
            </Button>
            
            <Button
              icon={<CheckCircleOutlined />}
              onClick={loadFromLocal}
              size="large"
            >
              加载本地配置
            </Button>
          </Space>
        </Form>
      </Card>

      <Card style={{ marginTop: '24px' }} title="📋 配置说明">
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <div>
            <Text strong>Tushare Token:</Text>
            <br />
            <Text type="secondary">
              访问 <a href="https://tushare.pro/" target="_blank" rel="noopener noreferrer">https://tushare.pro/</a> 注册并获取Token
            </Text>
          </div>
          
          <div>
            <Text strong>DeepSeek API Key:</Text>
            <br />
            <Text type="secondary">
              访问 <a href="https://platform.deepseek.com/" target="_blank" rel="noopener noreferrer">https://platform.deepseek.com/</a> 注册并创建API Key
            </Text>
          </div>

          <Alert
            message="配置文件位置"
            description="配置需要保存到：backend/config/user_config.json"
            type="warning"
            showIcon
          />
        </Space>
      </Card>

      <Card style={{ marginTop: '24px' }} title="🛠️ 其他配置方式">
        <Space direction="vertical" size="small" style={{ width: '100%' }}>
          <Text>• 运行命令行工具：<Text code>update_tokens.bat</Text></Text>
          <Text>• 直接编辑配置文件：<Text code>backend/config/user_config.json</Text></Text>
          <Text>• 使用Electron启动器的配置功能</Text>
        </Space>
      </Card>
    </div>
  );
};

export default SimpleSettings;
