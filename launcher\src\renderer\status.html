<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统状态 - 量化交易监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-left {
            display: flex;
            align-items: center;
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin-right: 20px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-2px);
        }

        .header h1 {
            font-size: 2em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .refresh-btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .refresh-btn:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }

        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .status-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-section h2 {
            font-size: 1.3em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .service-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            margin-bottom: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .service-info {
            flex: 1;
        }

        .service-name {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .service-details {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
        }

        .service-status {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4d4f;
        }

        .status-dot.running {
            background: #52c41a;
            animation: pulse 2s infinite;
        }

        .status-dot.starting {
            background: #faad14;
            animation: pulse 1s infinite;
        }

        .status-dot.error {
            background: #ff4d4f;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.6; }
        }

        .action-buttons {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            flex: 1;
        }

        .btn:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .btn-success {
            background: #52c41a;
        }

        .btn-success:hover {
            background: #73d13d;
        }

        .btn-danger {
            background: #ff4d4f;
        }

        .btn-danger:hover {
            background: #ff7875;
        }

        .btn-warning {
            background: #faad14;
        }

        .btn-warning:hover {
            background: #ffc53d;
        }

        .log-section {
            grid-column: 1 / -1;
            max-height: 400px;
        }

        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 4px;
        }

        .log-entry.info {
            color: #91d5ff;
        }

        .log-entry.error {
            color: #ffccc7;
            background: rgba(255, 77, 79, 0.1);
        }

        .log-entry.warning {
            color: #ffe58f;
            background: rgba(250, 173, 20, 0.1);
        }

        .log-timestamp {
            color: rgba(255, 255, 255, 0.5);
            margin-right: 10px;
        }

        .url-section {
            margin-top: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }

        .url-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .url-link {
            color: #40a9ff;
            text-decoration: none;
            cursor: pointer;
        }

        .url-link:hover {
            text-decoration: underline;
        }

        .health-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
        }

        .health-dot {
            width: 16px;
            height: 16px;
            border-radius: 50%;
            background: #ff4d4f;
        }

        .health-dot.healthy {
            background: #52c41a;
        }

        .health-dot.warning {
            background: #faad14;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 2px solid #1890ff;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <div class="header-left">
                <button class="back-btn" id="back-btn">← 返回</button>
                <h1>📊 系统状态</h1>
            </div>
            <button class="refresh-btn" id="refresh-btn">🔄 刷新</button>
        </header>

        <main class="main-content">
            <!-- 服务状态 -->
            <div class="status-section">
                <h2>🔗 服务状态</h2>
                
                <div class="health-indicator" id="overall-health">
                    <div class="health-dot" id="health-dot"></div>
                    <span id="health-text">检查中...</span>
                </div>

                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">后端API服务</div>
                        <div class="service-details" id="backend-details">端口: 8000 | PID: - | 响应时间: -</div>
                    </div>
                    <div class="service-status">
                        <div class="status-dot" id="backend-status"></div>
                        <span id="backend-status-text">停止</span>
                    </div>
                </div>

                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">前端UI服务</div>
                        <div class="service-details" id="frontend-details">端口: 3001 | PID: - | 响应时间: -</div>
                    </div>
                    <div class="service-status">
                        <div class="status-dot" id="frontend-status"></div>
                        <span id="frontend-status-text">停止</span>
                    </div>
                </div>

                <div class="action-buttons">
                    <button class="btn btn-success" id="start-btn">▶️ 启动服务</button>
                    <button class="btn btn-danger" id="stop-btn">⏹️ 停止服务</button>
                    <button class="btn btn-warning" id="restart-btn">🔄 重启服务</button>
                </div>

                <div class="url-section" id="url-section" style="display: none;">
                    <h3 style="margin-bottom: 10px;">🌐 访问地址</h3>
                    <div class="url-item">
                        <span>主界面:</span>
                        <a href="#" class="url-link" id="frontend-url">http://localhost:3001</a>
                    </div>
                    <div class="url-item">
                        <span>API文档:</span>
                        <a href="#" class="url-link" id="backend-url">http://localhost:8000/docs</a>
                    </div>
                </div>
            </div>

            <!-- 数据源状态 -->
            <div class="status-section">
                <h2>📡 数据源状态</h2>
                
                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">Tushare Pro</div>
                        <div class="service-details">主要数据源 | A股+港股</div>
                    </div>
                    <div class="service-status">
                        <div class="status-dot" id="tushare-status"></div>
                        <span id="tushare-status-text">未配置</span>
                    </div>
                </div>

                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">万得 Wind</div>
                        <div class="service-details">机构级数据 | 全市场</div>
                    </div>
                    <div class="service-status">
                        <div class="status-dot" id="wind-status"></div>
                        <span id="wind-status-text">未启用</span>
                    </div>
                </div>

                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">米筐 RiceQuant</div>
                        <div class="service-details">性价比数据 | A股+港股+美股</div>
                    </div>
                    <div class="service-status">
                        <div class="status-dot" id="ricequant-status"></div>
                        <span id="ricequant-status-text">未启用</span>
                    </div>
                </div>

                <div class="service-item">
                    <div class="service-info">
                        <div class="service-name">DeepSeek AI</div>
                        <div class="service-details">AI分析 | 情绪分析</div>
                    </div>
                    <div class="service-status">
                        <div class="status-dot running" id="deepseek-status"></div>
                        <span id="deepseek-status-text">已配置</span>
                    </div>
                </div>
            </div>

            <!-- 系统日志 -->
            <div class="status-section log-section">
                <h2>📋 系统日志</h2>
                <div class="log-container" id="log-container">
                    <div class="log-entry info">
                        <span class="log-timestamp">[2025-01-31 10:30:00]</span>
                        <span>系统启动完成</span>
                    </div>
                </div>
            </div>
        </main>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div id="loading-text">正在处理...</div>
        </div>
    </div>

    <script src="js/status.js"></script>
</body>
</html>
