"""
配置管理API端点
"""
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, <PERSON>
from typing import Dict, Any, Optional
import os
import json
import tushare as ts
from pathlib import Path

from app.core.config import Settings
from app.services.deepseek_service import DeepSeekService

router = APIRouter()

# 配置文件路径
CONFIG_DIR = Path("config")
CONFIG_FILE = CONFIG_DIR / "user_config.json"

class TokenConfig(BaseModel):
    """Token配置模型"""
    tushare_token: str = Field(..., min_length=32, description="Tushare Pro Token")
    deepseek_api_key: str = Field(..., regex=r"^sk-", description="DeepSeek API Key")
    enabled_features: Dict[str, bool] = Field(default={
        "ai_analysis": True,
        "realtime_data": True,
        "notifications": True
    })

class TokenTestRequest(BaseModel):
    """Token测试请求模型"""
    token_type: str = Field(..., description="Token类型: tushare 或 deepseek")
    token: str = Field(..., description="要测试的Token")

class TokenTestResponse(BaseModel):
    """Token测试响应模型"""
    valid: bool
    error: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

def ensure_config_dir():
    """确保配置目录存在"""
    CONFIG_DIR.mkdir(exist_ok=True)

def load_user_config() -> Dict[str, Any]:
    """加载用户配置"""
    ensure_config_dir()
    
    if CONFIG_FILE.exists():
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    # 返回默认配置
    return {
        "tushare_token": "",
        "deepseek_api_key": "***********************************",
        "enabled_features": {
            "ai_analysis": True,
            "realtime_data": True,
            "notifications": True
        }
    }

def save_user_config(config: Dict[str, Any]) -> bool:
    """保存用户配置"""
    ensure_config_dir()
    
    try:
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False

def test_tushare_token(token: str) -> TokenTestResponse:
    """测试Tushare Token"""
    try:
        # 设置token
        ts.set_token(token)
        pro = ts.pro_api()
        
        # 尝试获取基础数据来验证token
        df = pro.stock_basic(exchange='', list_status='L', fields='ts_code,symbol,name')
        
        if df is not None and len(df) > 0:
            return TokenTestResponse(
                valid=True,
                details={
                    "stock_count": len(df),
                    "message": "Token验证成功"
                }
            )
        else:
            return TokenTestResponse(
                valid=False,
                error="无法获取股票数据，请检查Token是否正确"
            )
            
    except Exception as e:
        error_msg = str(e)
        if "您的token不对" in error_msg or "token不对" in error_msg:
            return TokenTestResponse(
                valid=False,
                error="Token无效，请检查Token是否正确"
            )
        else:
            return TokenTestResponse(
                valid=False,
                error=f"连接失败: {error_msg}"
            )

def test_deepseek_token(token: str) -> TokenTestResponse:
    """测试DeepSeek Token"""
    try:
        # 创建DeepSeek服务实例
        service = DeepSeekService(api_key=token)
        
        # 发送测试请求
        test_prompt = "请回复'连接成功'来确认API连接正常。"
        response = service.analyze_market_sentiment(test_prompt)
        
        if response and "连接成功" in response:
            return TokenTestResponse(
                valid=True,
                details={
                    "message": "DeepSeek API连接成功"
                }
            )
        else:
            return TokenTestResponse(
                valid=True,  # 即使回复内容不完全匹配，只要有回复就说明连接成功
                details={
                    "message": "API连接成功",
                    "response": response[:100] if response else "无响应"
                }
            )
            
    except Exception as e:
        error_msg = str(e)
        if "401" in error_msg or "Unauthorized" in error_msg:
            return TokenTestResponse(
                valid=False,
                error="API Key无效，请检查Key是否正确"
            )
        elif "403" in error_msg or "Forbidden" in error_msg:
            return TokenTestResponse(
                valid=False,
                error="API Key权限不足或已过期"
            )
        else:
            return TokenTestResponse(
                valid=False,
                error=f"连接失败: {error_msg}"
            )

@router.get("/tokens", response_model=Dict[str, Any])
async def get_token_config():
    """获取Token配置"""
    try:
        config = load_user_config()
        
        # 隐藏敏感信息，只显示部分字符
        if config.get("tushare_token"):
            token = config["tushare_token"]
            config["tushare_token"] = token[:8] + "*" * (len(token) - 12) + token[-4:] if len(token) > 12 else token
        
        if config.get("deepseek_api_key"):
            key = config["deepseek_api_key"]
            config["deepseek_api_key"] = key[:8] + "*" * (len(key) - 12) + key[-4:] if len(key) > 12 else key
        
        return config
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@router.post("/tokens")
async def save_token_config(config: TokenConfig):
    """保存Token配置"""
    try:
        config_dict = config.dict()
        
        # 保存配置
        success = save_user_config(config_dict)
        
        if success:
            # 更新环境变量
            if config.tushare_token:
                os.environ["TUSHARE_TOKEN"] = config.tushare_token
            if config.deepseek_api_key:
                os.environ["DEEPSEEK_API_KEY"] = config.deepseek_api_key
            
            return {"success": True, "message": "配置保存成功"}
        else:
            raise HTTPException(status_code=500, detail="配置保存失败")
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存配置失败: {str(e)}")

@router.post("/test-token", response_model=TokenTestResponse)
async def test_token(request: TokenTestRequest):
    """测试Token有效性"""
    try:
        if request.token_type == "tushare":
            return test_tushare_token(request.token)
        elif request.token_type == "deepseek":
            return test_deepseek_token(request.token)
        else:
            raise HTTPException(
                status_code=400, 
                detail=f"不支持的Token类型: {request.token_type}"
            )
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"测试Token失败: {str(e)}")

@router.get("/status")
async def get_config_status():
    """获取配置状态"""
    try:
        config = load_user_config()
        
        status = {
            "tushare_configured": bool(config.get("tushare_token")),
            "deepseek_configured": bool(config.get("deepseek_api_key")),
            "features_enabled": config.get("enabled_features", {}),
            "config_file_exists": CONFIG_FILE.exists()
        }
        
        return status
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取状态失败: {str(e)}")

@router.delete("/tokens")
async def reset_config():
    """重置配置"""
    try:
        # 删除配置文件
        if CONFIG_FILE.exists():
            CONFIG_FILE.unlink()
        
        # 清除环境变量
        if "TUSHARE_TOKEN" in os.environ:
            del os.environ["TUSHARE_TOKEN"]
        if "DEEPSEEK_API_KEY" in os.environ:
            del os.environ["DEEPSEEK_API_KEY"]
        
        return {"success": True, "message": "配置已重置"}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置配置失败: {str(e)}")
