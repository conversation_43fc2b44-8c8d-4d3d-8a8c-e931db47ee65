@echo off
echo Starting Backend Only...
echo.

echo Current directory: %CD%
echo.

echo Changing to backend directory...
cd backend
echo Backend directory: %CD%
echo.

echo Checking if virtual environment exists...
if exist "venv" (
    echo Virtual environment found
) else (
    echo Creating virtual environment...
    python -m venv venv
)

echo.
echo Activating virtual environment...
call venv\Scripts\activate.bat

echo.
echo Installing basic dependencies...
pip install fastapi uvicorn

echo.
echo Starting backend server...
python -m uvicorn app.main:app --host 0.0.0.0 --port 8000

pause
