#!/usr/bin/env python3
"""
Token配置更新工具
简单的命令行工具来更新API Token配置
"""

import json
import os
from pathlib import Path

def get_config_file_path():
    """获取配置文件路径"""
    config_dir = Path("backend/config")
    config_dir.mkdir(exist_ok=True)
    return config_dir / "user_config.json"

def load_config():
    """加载现有配置"""
    config_file = get_config_file_path()
    
    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {e}")
    
    # 返回默认配置
    return {
        "tushare_token": "",
        "deepseek_api_key": "***********************************",
        "enabled_features": {
            "ai_analysis": True,
            "realtime_data": True,
            "notifications": True
        }
    }

def save_config(config):
    """保存配置"""
    config_file = get_config_file_path()
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
        return True
    except Exception as e:
        print(f"保存配置文件失败: {e}")
        return False

def validate_tushare_token(token):
    """验证Tushare Token格式"""
    if not token:
        return False, "Token不能为空"
    
    if len(token) < 32:
        return False, "Token长度应至少32位"
    
    return True, "格式验证通过"

def validate_deepseek_key(key):
    """验证DeepSeek API Key格式"""
    if not key:
        return False, "API Key不能为空"
    
    if not key.startswith('sk-'):
        return False, "API Key应以'sk-'开头"
    
    if len(key) < 10:
        return False, "API Key长度不足"
    
    return True, "格式验证通过"

def main():
    """主函数"""
    print("=" * 50)
    print("🔑 量化交易系统 - Token配置工具")
    print("=" * 50)
    
    # 加载现有配置
    config = load_config()
    
    print(f"\n📁 配置文件位置: {get_config_file_path()}")
    print(f"📊 当前配置状态:")
    print(f"   Tushare Token: {'已配置' if config.get('tushare_token') else '未配置'}")
    print(f"   DeepSeek API Key: {'已配置' if config.get('deepseek_api_key') else '未配置'}")
    
    while True:
        print("\n" + "=" * 30)
        print("请选择操作:")
        print("1. 更新 Tushare Token")
        print("2. 更新 DeepSeek API Key")
        print("3. 查看当前配置")
        print("4. 重置配置")
        print("5. 退出")
        print("=" * 30)
        
        choice = input("请输入选择 (1-5): ").strip()
        
        if choice == '1':
            print("\n🔧 更新 Tushare Token")
            current = config.get('tushare_token', '')
            if current:
                print(f"当前Token: {current[:8]}...{current[-4:]}")
            
            new_token = input("请输入新的Tushare Token (留空取消): ").strip()
            if new_token:
                valid, msg = validate_tushare_token(new_token)
                if valid:
                    config['tushare_token'] = new_token
                    if save_config(config):
                        print("✅ Tushare Token更新成功!")
                    else:
                        print("❌ 保存失败!")
                else:
                    print(f"❌ 验证失败: {msg}")
            else:
                print("取消更新")
        
        elif choice == '2':
            print("\n🔧 更新 DeepSeek API Key")
            current = config.get('deepseek_api_key', '')
            if current:
                print(f"当前API Key: {current[:8]}...{current[-4:]}")
            
            new_key = input("请输入新的DeepSeek API Key (留空取消): ").strip()
            if new_key:
                valid, msg = validate_deepseek_key(new_key)
                if valid:
                    config['deepseek_api_key'] = new_key
                    if save_config(config):
                        print("✅ DeepSeek API Key更新成功!")
                    else:
                        print("❌ 保存失败!")
                else:
                    print(f"❌ 验证失败: {msg}")
            else:
                print("取消更新")
        
        elif choice == '3':
            print("\n📋 当前配置:")
            print(json.dumps(config, indent=2, ensure_ascii=False))
        
        elif choice == '4':
            confirm = input("\n⚠️ 确定要重置所有配置吗? (y/N): ").strip().lower()
            if confirm == 'y':
                config = {
                    "tushare_token": "",
                    "deepseek_api_key": "***********************************",
                    "enabled_features": {
                        "ai_analysis": True,
                        "realtime_data": True,
                        "notifications": True
                    }
                }
                if save_config(config):
                    print("✅ 配置已重置!")
                else:
                    print("❌ 重置失败!")
            else:
                print("取消重置")
        
        elif choice == '5':
            print("\n👋 再见!")
            break
        
        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
