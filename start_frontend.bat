@echo off
echo ========================================
echo Starting Frontend Service
echo ========================================
echo.

echo Current directory: %CD%
echo.

echo Checking if frontend directory exists...
if not exist "frontend" (
    echo ERROR: frontend directory not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

echo Changing to frontend directory...
cd frontend
echo Frontend directory: %CD%

echo.
echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    pause
    exit /b 1
)

echo.
echo Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm not found
    pause
    exit /b 1
)

echo.
echo Installing/checking dependencies...
if not exist "node_modules" (
    echo Installing npm packages...
    npm install
) else (
    echo Dependencies already installed
)

echo.
echo Starting React development server...
echo Frontend will be available at: http://localhost:3001
echo.
echo Press Ctrl+C to stop the server
echo.

set PORT=3001
npm start

pause
