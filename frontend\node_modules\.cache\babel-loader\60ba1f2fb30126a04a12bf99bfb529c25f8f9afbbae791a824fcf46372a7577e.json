{"ast": null, "code": "var _jsxFileName = \"C:\\\\<PERSON>_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\App.tsx\";\nimport React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { Layout } from 'antd';\nimport Sidebar from './components/Layout/Sidebar.tsx';\nimport Header from './components/Layout/Header.tsx';\nimport Dashboard from './pages/Dashboard.tsx';\nimport StockManagement from './pages/StockManagement.tsx';\nimport Monitoring from './pages/Monitoring.tsx';\nimport AIAnalysis from './pages/AIAnalysis.tsx';\nimport SimpleSettings from './pages/SimpleSettings.tsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Content\n} = Layout;\nconst App = () => {\n  return /*#__PURE__*/_jsxDEV(Router, {\n    children: /*#__PURE__*/_jsxDEV(Layout, {\n      style: {\n        minHeight: '100vh'\n      },\n      children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Layout, {\n        children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Content, {\n          style: {\n            margin: '16px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Routes, {\n            children: [/*#__PURE__*/_jsxDEV(Route, {\n              path: \"/\",\n              element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 23,\n                columnNumber: 40\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 23,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/stocks\",\n              element: /*#__PURE__*/_jsxDEV(StockManagement, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 24,\n                columnNumber: 46\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 24,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/monitoring\",\n              element: /*#__PURE__*/_jsxDEV(Monitoring, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 25,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/indicators\",\n              element: /*#__PURE__*/_jsxDEV(Monitoring, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 26,\n                columnNumber: 50\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 26,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/ai\",\n              element: /*#__PURE__*/_jsxDEV(AIAnalysis, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 27,\n                columnNumber: 42\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 27,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Route, {\n              path: \"/settings\",\n              element: /*#__PURE__*/_jsxDEV(SimpleSettings, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 28,\n                columnNumber: 48\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 28,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 22,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_c = App;\nexport default App;\nvar _c;\n$RefreshReg$(_c, \"App\");", "map": {"version": 3, "names": ["React", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Layout", "Sidebar", "Header", "Dashboard", "StockManagement", "Monitoring", "AIAnalysis", "SimpleSettings", "jsxDEV", "_jsxDEV", "Content", "App", "children", "style", "minHeight", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "margin", "path", "element", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { BrowserRouter as Router, Routes, Route } from 'react-router-dom';\nimport { Layout } from 'antd';\nimport Sidebar from './components/Layout/Sidebar.tsx';\nimport Header from './components/Layout/Header.tsx';\nimport Dashboard from './pages/Dashboard.tsx';\nimport StockManagement from './pages/StockManagement.tsx';\nimport Monitoring from './pages/Monitoring.tsx';\nimport AIAnalysis from './pages/AIAnalysis.tsx';\nimport SimpleSettings from './pages/SimpleSettings.tsx';\n\nconst { Content } = Layout;\n\nconst App: React.FC = () => {\n  return (\n    <Router>\n      <Layout style={{ minHeight: '100vh' }}>\n        <Sidebar />\n        <Layout>\n          <Header />\n          <Content style={{ margin: '16px' }}>\n            <Routes>\n              <Route path=\"/\" element={<Dashboard />} />\n              <Route path=\"/stocks\" element={<StockManagement />} />\n              <Route path=\"/monitoring\" element={<Monitoring />} />\n              <Route path=\"/indicators\" element={<Monitoring />} />\n              <Route path=\"/ai\" element={<AIAnalysis />} />\n              <Route path=\"/settings\" element={<SimpleSettings />} />\n            </Routes>\n          </Content>\n        </Layout>\n      </Layout>\n    </Router>\n  );\n};\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,aAAa,IAAIC,MAAM,EAAEC,MAAM,EAAEC,KAAK,QAAQ,kBAAkB;AACzE,SAASC,MAAM,QAAQ,MAAM;AAC7B,OAAOC,OAAO,MAAM,iCAAiC;AACrD,OAAOC,MAAM,MAAM,gCAAgC;AACnD,OAAOC,SAAS,MAAM,uBAAuB;AAC7C,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,cAAc,MAAM,4BAA4B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExD,MAAM;EAAEC;AAAQ,CAAC,GAAGV,MAAM;AAE1B,MAAMW,GAAa,GAAGA,CAAA,KAAM;EAC1B,oBACEF,OAAA,CAACZ,MAAM;IAAAe,QAAA,eACLH,OAAA,CAACT,MAAM;MAACa,KAAK,EAAE;QAAEC,SAAS,EAAE;MAAQ,CAAE;MAAAF,QAAA,gBACpCH,OAAA,CAACR,OAAO;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eACXT,OAAA,CAACT,MAAM;QAAAY,QAAA,gBACLH,OAAA,CAACP,MAAM;UAAAa,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACVT,OAAA,CAACC,OAAO;UAACG,KAAK,EAAE;YAAEM,MAAM,EAAE;UAAO,CAAE;UAAAP,QAAA,eACjCH,OAAA,CAACX,MAAM;YAAAc,QAAA,gBACLH,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,GAAG;cAACC,OAAO,eAAEZ,OAAA,CAACN,SAAS;gBAAAY,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC1CT,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,SAAS;cAACC,OAAO,eAAEZ,OAAA,CAACL,eAAe;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACtDT,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,aAAa;cAACC,OAAO,eAAEZ,OAAA,CAACJ,UAAU;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDT,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,aAAa;cAACC,OAAO,eAAEZ,OAAA,CAACJ,UAAU;gBAAAU,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eACrDT,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,KAAK;cAACC,OAAO,eAAEZ,OAAA,CAACH,UAAU;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC7CT,OAAA,CAACV,KAAK;cAACqB,IAAI,EAAC,WAAW;cAACC,OAAO,eAAEZ,OAAA,CAACF,cAAc;gBAAAQ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAE;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEb,CAAC;AAACI,EAAA,GArBIX,GAAa;AAuBnB,eAAeA,GAAG;AAAC,IAAAW,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}