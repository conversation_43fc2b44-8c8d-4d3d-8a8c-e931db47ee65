const { app } = require('electron');
const fs = require('fs').promises;
const path = require('path');
const axios = require('axios');

class ConfigManager {
  constructor() {
    this.configPath = path.join(app.getPath('userData'), 'config.json');
    this.backupPath = path.join(app.getPath('userData'), 'config.backup.json');
    
    this.defaultConfig = {
      tushare: {
        token: '',
        enabled: true,
        status: 'unconfigured' // unconfigured, valid, invalid
      },
      wind: {
        username: '',
        password: '',
        enabled: false,
        status: 'unconfigured'
      },
      ricequant: {
        api_key: '',
        enabled: false,
        status: 'unconfigured'
      },
      deepseek: {
        api_key: '***********************************',
        base_url: 'https://api.deepseek.com',
        enabled: true,
        status: 'configured'
      },
      system: {
        first_run: true,
        auto_start: true,
        theme: 'dark',
        language: 'zh-CN',
        backend_port: 8000,
        frontend_port: 3001,
        log_level: 'INFO'
      },
      ui: {
        window_width: 1000,
        window_height: 700,
        remember_size: true,
        minimize_to_tray: false
      }
    };
  }

  async initialize() {
    try {
      // 确保用户数据目录存在
      const userDataPath = app.getPath('userData');
      await fs.mkdir(userDataPath, { recursive: true });
      
      // 加载配置
      await this.loadConfig();
      
      console.log('配置管理器初始化完成');
    } catch (error) {
      console.error('配置管理器初始化失败:', error);
      throw error;
    }
  }

  async loadConfig() {
    try {
      // 检查配置文件是否存在
      const exists = await this.fileExists(this.configPath);
      
      if (!exists) {
        // 首次运行，创建默认配置
        console.log('首次运行，创建默认配置');
        await this.saveConfig(this.defaultConfig);
        return this.defaultConfig;
      }

      // 读取配置文件
      const configData = await fs.readFile(this.configPath, 'utf8');
      const config = JSON.parse(configData);
      
      // 合并默认配置（处理新增的配置项）
      const mergedConfig = this.mergeConfig(this.defaultConfig, config);
      
      // 如果配置有更新，保存合并后的配置
      if (JSON.stringify(config) !== JSON.stringify(mergedConfig)) {
        await this.saveConfig(mergedConfig);
      }
      
      return mergedConfig;
      
    } catch (error) {
      console.error('加载配置失败:', error);
      
      // 尝试从备份恢复
      try {
        const backupExists = await this.fileExists(this.backupPath);
        if (backupExists) {
          console.log('尝试从备份恢复配置');
          const backupData = await fs.readFile(this.backupPath, 'utf8');
          const backupConfig = JSON.parse(backupData);
          await this.saveConfig(backupConfig);
          return backupConfig;
        }
      } catch (backupError) {
        console.error('备份恢复失败:', backupError);
      }
      
      // 使用默认配置
      console.log('使用默认配置');
      await this.saveConfig(this.defaultConfig);
      return this.defaultConfig;
    }
  }

  async saveConfig(config) {
    try {
      // 创建备份
      const exists = await this.fileExists(this.configPath);
      if (exists) {
        await fs.copyFile(this.configPath, this.backupPath);
      }
      
      // 保存配置
      const configData = JSON.stringify(config, null, 2);
      await fs.writeFile(this.configPath, configData, 'utf8');
      
      console.log('配置保存成功');
      return true;
      
    } catch (error) {
      console.error('保存配置失败:', error);
      throw error;
    }
  }

  async validateToken(source, token) {
    try {
      console.log(`验证${source}令牌...`);
      
      switch (source) {
        case 'tushare':
          return await this.validateTushareToken(token);
        case 'wind':
          return await this.validateWindCredentials(token);
        case 'ricequant':
          return await this.validateRiceQuantToken(token);
        case 'deepseek':
          return await this.validateDeepSeekToken(token);
        default:
          throw new Error(`不支持的数据源: ${source}`);
      }
      
    } catch (error) {
      console.error(`验证${source}令牌失败:`, error);
      return {
        valid: false,
        error: error.message
      };
    }
  }

  async validateTushareToken(token) {
    try {
      // 基础格式检查
      if (!token || token.length < 32) {
        return {
          valid: false,
          error: 'Token格式不正确，应为32位以上字符串'
        };
      }

      // 检查是否为有效的十六进制字符串
      if (!/^[a-fA-F0-9]+$/.test(token)) {
        return {
          valid: false,
          error: 'Token应为十六进制字符串'
        };
      }

      // 尝试调用后端API验证（如果后端正在运行）
      try {
        const response = await axios.post('http://localhost:8000/api/v1/validate-token', {
          source: 'tushare',
          token: token
        }, { timeout: 5000 });

        if (response.data && response.data.valid) {
          return {
            valid: true,
            message: 'Token验证成功'
          };
        } else {
          return {
            valid: false,
            error: response.data?.error || 'Token验证失败'
          };
        }
      } catch (apiError) {
        // 如果后端不可用，只进行格式验证
        console.log('后端API不可用，仅进行格式验证');
        return {
          valid: true,
          message: 'Token格式正确（未连接到后端验证）'
        };
      }

    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  async validateWindCredentials(credentials) {
    try {
      const { username, password } = credentials;
      
      if (!username || !password) {
        return {
          valid: false,
          error: '用户名和密码不能为空'
        };
      }
      
      // TODO: 实际验证逻辑
      return {
        valid: true,
        message: '凭据格式正确'
      };
      
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  async validateRiceQuantToken(token) {
    try {
      if (!token || token.length < 16) {
        return {
          valid: false,
          error: 'API Key格式不正确'
        };
      }
      
      // TODO: 实际验证逻辑
      return {
        valid: true,
        message: 'API Key格式正确'
      };
      
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  async validateDeepSeekToken(token) {
    try {
      if (!token || !token.startsWith('sk-')) {
        return {
          valid: false,
          error: 'DeepSeek API Key格式不正确'
        };
      }
      
      // TODO: 实际验证逻辑
      return {
        valid: true,
        message: 'API Key格式正确'
      };
      
    } catch (error) {
      return {
        valid: false,
        error: error.message
      };
    }
  }

  mergeConfig(defaultConfig, userConfig) {
    const merged = { ...defaultConfig };
    
    for (const key in userConfig) {
      if (typeof userConfig[key] === 'object' && !Array.isArray(userConfig[key])) {
        merged[key] = { ...defaultConfig[key], ...userConfig[key] };
      } else {
        merged[key] = userConfig[key];
      }
    }
    
    return merged;
  }

  async fileExists(filePath) {
    try {
      await fs.access(filePath);
      return true;
    } catch {
      return false;
    }
  }

  async resetConfig() {
    try {
      await this.saveConfig(this.defaultConfig);
      return true;
    } catch (error) {
      console.error('重置配置失败:', error);
      throw error;
    }
  }

  async exportConfig() {
    try {
      const config = await this.loadConfig();
      
      // 移除敏感信息
      const exportConfig = JSON.parse(JSON.stringify(config));
      exportConfig.tushare.token = exportConfig.tushare.token ? '***' : '';
      exportConfig.wind.password = exportConfig.wind.password ? '***' : '';
      exportConfig.ricequant.api_key = exportConfig.ricequant.api_key ? '***' : '';
      exportConfig.deepseek.api_key = exportConfig.deepseek.api_key ? '***' : '';
      
      return exportConfig;
    } catch (error) {
      console.error('导出配置失败:', error);
      throw error;
    }
  }
}

module.exports = ConfigManager;
