{"ast": null, "code": "import * as React from 'react';\nimport ResizeObserver from 'rc-resize-observer';\nimport useLayoutEffect from \"rc-util/es/hooks/useLayoutEffect\";\nexport default function MeasureCell(_ref) {\n  var columnKey = _ref.columnKey,\n    onColumnResize = _ref.onColumnResize;\n  var cellRef = React.useRef();\n  useLayoutEffect(function () {\n    if (cellRef.current) {\n      onColumnResize(columnKey, cellRef.current.offsetWidth);\n    }\n  }, []);\n  return /*#__PURE__*/React.createElement(ResizeObserver, {\n    data: columnKey\n  }, /*#__PURE__*/React.createElement(\"td\", {\n    ref: cellRef,\n    style: {\n      padding: 0,\n      border: 0,\n      height: 0\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      height: 0,\n      overflow: 'hidden'\n    }\n  }, \"\\xA0\")));\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}