{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Line path for bezier and straight line draw\r\n */\nimport * as graphic from '../../util/graphic.js';\nimport * as vec2 from 'zrender/lib/core/vector.js';\nvar straightLineProto = graphic.Line.prototype;\nvar bezierCurveProto = graphic.BezierCurve.prototype;\nvar StraightLineShape = /** @class */function () {\n  function StraightLineShape() {\n    // Start point\n    this.x1 = 0;\n    this.y1 = 0;\n    // End point\n    this.x2 = 0;\n    this.y2 = 0;\n    this.percent = 1;\n  }\n  return StraightLineShape;\n}();\nvar CurveShape = /** @class */function (_super) {\n  __extends(CurveShape, _super);\n  function CurveShape() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  return CurveShape;\n}(StraightLineShape);\nfunction isStraightLine(shape) {\n  return isNaN(+shape.cpx1) || isNaN(+shape.cpy1);\n}\nvar ECLinePath = /** @class */function (_super) {\n  __extends(ECLinePath, _super);\n  function ECLinePath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'ec-line';\n    return _this;\n  }\n  ECLinePath.prototype.getDefaultStyle = function () {\n    return {\n      stroke: '#000',\n      fill: null\n    };\n  };\n  ECLinePath.prototype.getDefaultShape = function () {\n    return new StraightLineShape();\n  };\n  ECLinePath.prototype.buildPath = function (ctx, shape) {\n    if (isStraightLine(shape)) {\n      straightLineProto.buildPath.call(this, ctx, shape);\n    } else {\n      bezierCurveProto.buildPath.call(this, ctx, shape);\n    }\n  };\n  ECLinePath.prototype.pointAt = function (t) {\n    if (isStraightLine(this.shape)) {\n      return straightLineProto.pointAt.call(this, t);\n    } else {\n      return bezierCurveProto.pointAt.call(this, t);\n    }\n  };\n  ECLinePath.prototype.tangentAt = function (t) {\n    var shape = this.shape;\n    var p = isStraightLine(shape) ? [shape.x2 - shape.x1, shape.y2 - shape.y1] : bezierCurveProto.tangentAt.call(this, t);\n    return vec2.normalize(p, p);\n  };\n  return ECLinePath;\n}(graphic.Path);\nexport default ECLinePath;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}