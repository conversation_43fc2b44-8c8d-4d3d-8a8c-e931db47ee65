{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport { unionAxisExtentFromData } from '../../coord/axisHelper.js';\nimport { ensureScaleRawExtentInfo } from '../../coord/scaleRawExtentInfo.js';\nimport { getAxisMainType, isCoordSupported } from './helper.js';\nimport { SINGLE_REFERRING } from '../../util/model.js';\nvar each = zrUtil.each;\nvar asc = numberUtil.asc;\n/**\r\n * Operate single axis.\r\n * One axis can only operated by one axis operator.\r\n * Different dataZoomModels may be defined to operate the same axis.\r\n * (i.e. 'inside' data zoom and 'slider' data zoom components)\r\n * So dataZoomModels share one axisProxy in that case.\r\n */\nvar AxisProxy = /** @class */function () {\n  function AxisProxy(dimName, axisIndex, dataZoomModel, ecModel) {\n    this._dimName = dimName;\n    this._axisIndex = axisIndex;\n    this.ecModel = ecModel;\n    this._dataZoomModel = dataZoomModel;\n    // /**\n    //  * @readOnly\n    //  * @private\n    //  */\n    // this.hasSeriesStacked;\n  }\n  /**\r\n   * Whether the axisProxy is hosted by dataZoomModel.\r\n   */\n  AxisProxy.prototype.hostedBy = function (dataZoomModel) {\n    return this._dataZoomModel === dataZoomModel;\n  };\n  /**\r\n   * @return Value can only be NaN or finite value.\r\n   */\n  AxisProxy.prototype.getDataValueWindow = function () {\n    return this._valueWindow.slice();\n  };\n  /**\r\n   * @return {Array.<number>}\r\n   */\n  AxisProxy.prototype.getDataPercentWindow = function () {\n    return this._percentWindow.slice();\n  };\n  AxisProxy.prototype.getTargetSeriesModels = function () {\n    var seriesModels = [];\n    this.ecModel.eachSeries(function (seriesModel) {\n      if (isCoordSupported(seriesModel)) {\n        var axisMainType = getAxisMainType(this._dimName);\n        var axisModel = seriesModel.getReferringComponents(axisMainType, SINGLE_REFERRING).models[0];\n        if (axisModel && this._axisIndex === axisModel.componentIndex) {\n          seriesModels.push(seriesModel);\n        }\n      }\n    }, this);\n    return seriesModels;\n  };\n  AxisProxy.prototype.getAxisModel = function () {\n    return this.ecModel.getComponent(this._dimName + 'Axis', this._axisIndex);\n  };\n  AxisProxy.prototype.getMinMaxSpan = function () {\n    return zrUtil.clone(this._minMaxSpan);\n  };\n  /**\r\n   * Only calculate by given range and this._dataExtent, do not change anything.\r\n   */\n  AxisProxy.prototype.calculateDataWindow = function (opt) {\n    var dataExtent = this._dataExtent;\n    var axisModel = this.getAxisModel();\n    var scale = axisModel.axis.scale;\n    var rangePropMode = this._dataZoomModel.getRangePropMode();\n    var percentExtent = [0, 100];\n    var percentWindow = [];\n    var valueWindow = [];\n    var hasPropModeValue;\n    each(['start', 'end'], function (prop, idx) {\n      var boundPercent = opt[prop];\n      var boundValue = opt[prop + 'Value'];\n      // Notice: dataZoom is based either on `percentProp` ('start', 'end') or\n      // on `valueProp` ('startValue', 'endValue'). (They are based on the data extent\n      // but not min/max of axis, which will be calculated by data window then).\n      // The former one is suitable for cases that a dataZoom component controls multiple\n      // axes with different unit or extent, and the latter one is suitable for accurate\n      // zoom by pixel (e.g., in dataZoomSelect).\n      // we use `getRangePropMode()` to mark which prop is used. `rangePropMode` is updated\n      // only when setOption or dispatchAction, otherwise it remains its original value.\n      // (Why not only record `percentProp` and always map to `valueProp`? Because\n      // the map `valueProp` -> `percentProp` -> `valueProp` probably not the original\n      // `valueProp`. consider two axes constrolled by one dataZoom. They have different\n      // data extent. All of values that are overflow the `dataExtent` will be calculated\n      // to percent '100%').\n      if (rangePropMode[idx] === 'percent') {\n        boundPercent == null && (boundPercent = percentExtent[idx]);\n        // Use scale.parse to math round for category or time axis.\n        boundValue = scale.parse(numberUtil.linearMap(boundPercent, percentExtent, dataExtent));\n      } else {\n        hasPropModeValue = true;\n        boundValue = boundValue == null ? dataExtent[idx] : scale.parse(boundValue);\n        // Calculating `percent` from `value` may be not accurate, because\n        // This calculation can not be inversed, because all of values that\n        // are overflow the `dataExtent` will be calculated to percent '100%'\n        boundPercent = numberUtil.linearMap(boundValue, dataExtent, percentExtent);\n      }\n      // valueWindow[idx] = round(boundValue);\n      // percentWindow[idx] = round(boundPercent);\n      // fallback to extent start/end when parsed value or percent is invalid\n      valueWindow[idx] = boundValue == null || isNaN(boundValue) ? dataExtent[idx] : boundValue;\n      percentWindow[idx] = boundPercent == null || isNaN(boundPercent) ? percentExtent[idx] : boundPercent;\n    });\n    asc(valueWindow);\n    asc(percentWindow);\n    // The windows from user calling of `dispatchAction` might be out of the extent,\n    // or do not obey the `min/maxSpan`, `min/maxValueSpan`. But we don't restrict window\n    // by `zoomLock` here, because we see `zoomLock` just as a interaction constraint,\n    // where API is able to initialize/modify the window size even though `zoomLock`\n    // specified.\n    var spans = this._minMaxSpan;\n    hasPropModeValue ? restrictSet(valueWindow, percentWindow, dataExtent, percentExtent, false) : restrictSet(percentWindow, valueWindow, percentExtent, dataExtent, true);\n    function restrictSet(fromWindow, toWindow, fromExtent, toExtent, toValue) {\n      var suffix = toValue ? 'Span' : 'ValueSpan';\n      sliderMove(0, fromWindow, fromExtent, 'all', spans['min' + suffix], spans['max' + suffix]);\n      for (var i = 0; i < 2; i++) {\n        toWindow[i] = numberUtil.linearMap(fromWindow[i], fromExtent, toExtent, true);\n        toValue && (toWindow[i] = scale.parse(toWindow[i]));\n      }\n    }\n    return {\n      valueWindow: valueWindow,\n      percentWindow: percentWindow\n    };\n  };\n  /**\r\n   * Notice: reset should not be called before series.restoreData() is called,\r\n   * so it is recommended to be called in \"process stage\" but not \"model init\r\n   * stage\".\r\n   */\n  AxisProxy.prototype.reset = function (dataZoomModel) {\n    if (dataZoomModel !== this._dataZoomModel) {\n      return;\n    }\n    var targetSeries = this.getTargetSeriesModels();\n    // Culculate data window and data extent, and record them.\n    this._dataExtent = calculateDataExtent(this, this._dimName, targetSeries);\n    // `calculateDataWindow` uses min/maxSpan.\n    this._updateMinMaxSpan();\n    var dataWindow = this.calculateDataWindow(dataZoomModel.settledOption);\n    this._valueWindow = dataWindow.valueWindow;\n    this._percentWindow = dataWindow.percentWindow;\n    // Update axis setting then.\n    this._setAxisModel();\n  };\n  AxisProxy.prototype.filterData = function (dataZoomModel, api) {\n    if (dataZoomModel !== this._dataZoomModel) {\n      return;\n    }\n    var axisDim = this._dimName;\n    var seriesModels = this.getTargetSeriesModels();\n    var filterMode = dataZoomModel.get('filterMode');\n    var valueWindow = this._valueWindow;\n    if (filterMode === 'none') {\n      return;\n    }\n    // FIXME\n    // Toolbox may has dataZoom injected. And if there are stacked bar chart\n    // with NaN data, NaN will be filtered and stack will be wrong.\n    // So we need to force the mode to be set empty.\n    // In fect, it is not a big deal that do not support filterMode-'filter'\n    // when using toolbox#dataZoom, utill tooltip#dataZoom support \"single axis\n    // selection\" some day, which might need \"adapt to data extent on the\n    // otherAxis\", which is disabled by filterMode-'empty'.\n    // But currently, stack has been fixed to based on value but not index,\n    // so this is not an issue any more.\n    // let otherAxisModel = this.getOtherAxisModel();\n    // if (dataZoomModel.get('$fromToolbox')\n    //     && otherAxisModel\n    //     && otherAxisModel.hasSeriesStacked\n    // ) {\n    //     filterMode = 'empty';\n    // }\n    // TODO\n    // filterMode 'weakFilter' and 'empty' is not optimized for huge data yet.\n    each(seriesModels, function (seriesModel) {\n      var seriesData = seriesModel.getData();\n      var dataDims = seriesData.mapDimensionsAll(axisDim);\n      if (!dataDims.length) {\n        return;\n      }\n      if (filterMode === 'weakFilter') {\n        var store_1 = seriesData.getStore();\n        var dataDimIndices_1 = zrUtil.map(dataDims, function (dim) {\n          return seriesData.getDimensionIndex(dim);\n        }, seriesData);\n        seriesData.filterSelf(function (dataIndex) {\n          var leftOut;\n          var rightOut;\n          var hasValue;\n          for (var i = 0; i < dataDims.length; i++) {\n            var value = store_1.get(dataDimIndices_1[i], dataIndex);\n            var thisHasValue = !isNaN(value);\n            var thisLeftOut = value < valueWindow[0];\n            var thisRightOut = value > valueWindow[1];\n            if (thisHasValue && !thisLeftOut && !thisRightOut) {\n              return true;\n            }\n            thisHasValue && (hasValue = true);\n            thisLeftOut && (leftOut = true);\n            thisRightOut && (rightOut = true);\n          }\n          // If both left out and right out, do not filter.\n          return hasValue && leftOut && rightOut;\n        });\n      } else {\n        each(dataDims, function (dim) {\n          if (filterMode === 'empty') {\n            seriesModel.setData(seriesData = seriesData.map(dim, function (value) {\n              return !isInWindow(value) ? NaN : value;\n            }));\n          } else {\n            var range = {};\n            range[dim] = valueWindow;\n            // console.time('select');\n            seriesData.selectRange(range);\n            // console.timeEnd('select');\n          }\n        });\n      }\n      each(dataDims, function (dim) {\n        seriesData.setApproximateExtent(valueWindow, dim);\n      });\n    });\n    function isInWindow(value) {\n      return value >= valueWindow[0] && value <= valueWindow[1];\n    }\n  };\n  AxisProxy.prototype._updateMinMaxSpan = function () {\n    var minMaxSpan = this._minMaxSpan = {};\n    var dataZoomModel = this._dataZoomModel;\n    var dataExtent = this._dataExtent;\n    each(['min', 'max'], function (minMax) {\n      var percentSpan = dataZoomModel.get(minMax + 'Span');\n      var valueSpan = dataZoomModel.get(minMax + 'ValueSpan');\n      valueSpan != null && (valueSpan = this.getAxisModel().axis.scale.parse(valueSpan));\n      // minValueSpan and maxValueSpan has higher priority than minSpan and maxSpan\n      if (valueSpan != null) {\n        percentSpan = numberUtil.linearMap(dataExtent[0] + valueSpan, dataExtent, [0, 100], true);\n      } else if (percentSpan != null) {\n        valueSpan = numberUtil.linearMap(percentSpan, [0, 100], dataExtent, true) - dataExtent[0];\n      }\n      minMaxSpan[minMax + 'Span'] = percentSpan;\n      minMaxSpan[minMax + 'ValueSpan'] = valueSpan;\n    }, this);\n  };\n  AxisProxy.prototype._setAxisModel = function () {\n    var axisModel = this.getAxisModel();\n    var percentWindow = this._percentWindow;\n    var valueWindow = this._valueWindow;\n    if (!percentWindow) {\n      return;\n    }\n    // [0, 500]: arbitrary value, guess axis extent.\n    var precision = numberUtil.getPixelPrecision(valueWindow, [0, 500]);\n    precision = Math.min(precision, 20);\n    // For value axis, if min/max/scale are not set, we just use the extent obtained\n    // by series data, which may be a little different from the extent calculated by\n    // `axisHelper.getScaleExtent`. But the different just affects the experience a\n    // little when zooming. So it will not be fixed until some users require it strongly.\n    var rawExtentInfo = axisModel.axis.scale.rawExtentInfo;\n    if (percentWindow[0] !== 0) {\n      rawExtentInfo.setDeterminedMinMax('min', +valueWindow[0].toFixed(precision));\n    }\n    if (percentWindow[1] !== 100) {\n      rawExtentInfo.setDeterminedMinMax('max', +valueWindow[1].toFixed(precision));\n    }\n    rawExtentInfo.freeze();\n  };\n  return AxisProxy;\n}();\nfunction calculateDataExtent(axisProxy, axisDim, seriesModels) {\n  var dataExtent = [Infinity, -Infinity];\n  each(seriesModels, function (seriesModel) {\n    unionAxisExtentFromData(dataExtent, seriesModel.getData(), axisDim);\n  });\n  // It is important to get \"consistent\" extent when more then one axes is\n  // controlled by a `dataZoom`, otherwise those axes will not be synchronized\n  // when zooming. But it is difficult to know what is \"consistent\", considering\n  // axes have different type or even different meanings (For example, two\n  // time axes are used to compare data of the same date in different years).\n  // So basically dataZoom just obtains extent by series.data (in category axis\n  // extent can be obtained from axis.data).\n  // Nevertheless, user can set min/max/scale on axes to make extent of axes\n  // consistent.\n  var axisModel = axisProxy.getAxisModel();\n  var rawExtentResult = ensureScaleRawExtentInfo(axisModel.axis.scale, axisModel, dataExtent).calculate();\n  return [rawExtentResult.min, rawExtentResult.max];\n}\nexport default AxisProxy;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}