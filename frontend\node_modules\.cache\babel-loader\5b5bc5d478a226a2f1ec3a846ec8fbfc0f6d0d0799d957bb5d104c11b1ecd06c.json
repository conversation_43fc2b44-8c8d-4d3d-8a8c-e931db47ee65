{"ast": null, "code": "import Group from '../graphic/Group.js';\nimport ZRImage from '../graphic/Image.js';\nimport Circle from '../graphic/shape/Circle.js';\nimport Rect from '../graphic/shape/Rect.js';\nimport Ellipse from '../graphic/shape/Ellipse.js';\nimport Line from '../graphic/shape/Line.js';\nimport Polygon from '../graphic/shape/Polygon.js';\nimport Polyline from '../graphic/shape/Polyline.js';\nimport * as matrix from '../core/matrix.js';\nimport { createFromString } from './path.js';\nimport { defaults, trim, each, map, keys, hasOwn } from '../core/util.js';\nimport LinearGradient from '../graphic/LinearGradient.js';\nimport RadialGradient from '../graphic/RadialGradient.js';\nimport TSpan from '../graphic/TSpan.js';\nimport { parseXML } from './parseXML.js';\n;\nvar nodeParsers;\nvar INHERITABLE_STYLE_ATTRIBUTES_MAP = {\n  'fill': 'fill',\n  'stroke': 'stroke',\n  'stroke-width': 'lineWidth',\n  'opacity': 'opacity',\n  'fill-opacity': 'fillOpacity',\n  'stroke-opacity': 'strokeOpacity',\n  'stroke-dasharray': 'lineDash',\n  'stroke-dashoffset': 'lineDashOffset',\n  'stroke-linecap': 'lineCap',\n  'stroke-linejoin': 'lineJoin',\n  'stroke-miterlimit': 'miterLimit',\n  'font-family': 'fontFamily',\n  'font-size': 'fontSize',\n  'font-style': 'fontStyle',\n  'font-weight': 'fontWeight',\n  'text-anchor': 'textAlign',\n  'visibility': 'visibility',\n  'display': 'display'\n};\nvar INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS = keys(INHERITABLE_STYLE_ATTRIBUTES_MAP);\nvar SELF_STYLE_ATTRIBUTES_MAP = {\n  'alignment-baseline': 'textBaseline',\n  'stop-color': 'stopColor'\n};\nvar SELF_STYLE_ATTRIBUTES_MAP_KEYS = keys(SELF_STYLE_ATTRIBUTES_MAP);\nvar SVGParser = function () {\n  function SVGParser() {\n    this._defs = {};\n    this._root = null;\n  }\n  SVGParser.prototype.parse = function (xml, opt) {\n    opt = opt || {};\n    var svg = parseXML(xml);\n    if (process.env.NODE_ENV !== 'production') {\n      if (!svg) {\n        throw new Error('Illegal svg');\n      }\n    }\n    this._defsUsePending = [];\n    var root = new Group();\n    this._root = root;\n    var named = [];\n    var viewBox = svg.getAttribute('viewBox') || '';\n    var width = parseFloat(svg.getAttribute('width') || opt.width);\n    var height = parseFloat(svg.getAttribute('height') || opt.height);\n    isNaN(width) && (width = null);\n    isNaN(height) && (height = null);\n    parseAttributes(svg, root, null, true, false);\n    var child = svg.firstChild;\n    while (child) {\n      this._parseNode(child, root, named, null, false, false);\n      child = child.nextSibling;\n    }\n    applyDefs(this._defs, this._defsUsePending);\n    this._defsUsePending = [];\n    var viewBoxRect;\n    var viewBoxTransform;\n    if (viewBox) {\n      var viewBoxArr = splitNumberSequence(viewBox);\n      if (viewBoxArr.length >= 4) {\n        viewBoxRect = {\n          x: parseFloat(viewBoxArr[0] || 0),\n          y: parseFloat(viewBoxArr[1] || 0),\n          width: parseFloat(viewBoxArr[2]),\n          height: parseFloat(viewBoxArr[3])\n        };\n      }\n    }\n    if (viewBoxRect && width != null && height != null) {\n      viewBoxTransform = makeViewBoxTransform(viewBoxRect, {\n        x: 0,\n        y: 0,\n        width: width,\n        height: height\n      });\n      if (!opt.ignoreViewBox) {\n        var elRoot = root;\n        root = new Group();\n        root.add(elRoot);\n        elRoot.scaleX = elRoot.scaleY = viewBoxTransform.scale;\n        elRoot.x = viewBoxTransform.x;\n        elRoot.y = viewBoxTransform.y;\n      }\n    }\n    if (!opt.ignoreRootClip && width != null && height != null) {\n      root.setClipPath(new Rect({\n        shape: {\n          x: 0,\n          y: 0,\n          width: width,\n          height: height\n        }\n      }));\n    }\n    return {\n      root: root,\n      width: width,\n      height: height,\n      viewBoxRect: viewBoxRect,\n      viewBoxTransform: viewBoxTransform,\n      named: named\n    };\n  };\n  SVGParser.prototype._parseNode = function (xmlNode, parentGroup, named, namedFrom, isInDefs, isInText) {\n    var nodeName = xmlNode.nodeName.toLowerCase();\n    var el;\n    var namedFromForSub = namedFrom;\n    if (nodeName === 'defs') {\n      isInDefs = true;\n    }\n    if (nodeName === 'text') {\n      isInText = true;\n    }\n    if (nodeName === 'defs' || nodeName === 'switch') {\n      el = parentGroup;\n    } else {\n      if (!isInDefs) {\n        var parser_1 = nodeParsers[nodeName];\n        if (parser_1 && hasOwn(nodeParsers, nodeName)) {\n          el = parser_1.call(this, xmlNode, parentGroup);\n          var nameAttr = xmlNode.getAttribute('name');\n          if (nameAttr) {\n            var newNamed = {\n              name: nameAttr,\n              namedFrom: null,\n              svgNodeTagLower: nodeName,\n              el: el\n            };\n            named.push(newNamed);\n            if (nodeName === 'g') {\n              namedFromForSub = newNamed;\n            }\n          } else if (namedFrom) {\n            named.push({\n              name: namedFrom.name,\n              namedFrom: namedFrom,\n              svgNodeTagLower: nodeName,\n              el: el\n            });\n          }\n          parentGroup.add(el);\n        }\n      }\n      var parser = paintServerParsers[nodeName];\n      if (parser && hasOwn(paintServerParsers, nodeName)) {\n        var def = parser.call(this, xmlNode);\n        var id = xmlNode.getAttribute('id');\n        if (id) {\n          this._defs[id] = def;\n        }\n      }\n    }\n    if (el && el.isGroup) {\n      var child = xmlNode.firstChild;\n      while (child) {\n        if (child.nodeType === 1) {\n          this._parseNode(child, el, named, namedFromForSub, isInDefs, isInText);\n        } else if (child.nodeType === 3 && isInText) {\n          this._parseText(child, el);\n        }\n        child = child.nextSibling;\n      }\n    }\n  };\n  SVGParser.prototype._parseText = function (xmlNode, parentGroup) {\n    var text = new TSpan({\n      style: {\n        text: xmlNode.textContent\n      },\n      silent: true,\n      x: this._textX || 0,\n      y: this._textY || 0\n    });\n    inheritStyle(parentGroup, text);\n    parseAttributes(xmlNode, text, this._defsUsePending, false, false);\n    applyTextAlignment(text, parentGroup);\n    var textStyle = text.style;\n    var fontSize = textStyle.fontSize;\n    if (fontSize && fontSize < 9) {\n      textStyle.fontSize = 9;\n      text.scaleX *= fontSize / 9;\n      text.scaleY *= fontSize / 9;\n    }\n    var font = (textStyle.fontSize || textStyle.fontFamily) && [textStyle.fontStyle, textStyle.fontWeight, (textStyle.fontSize || 12) + 'px', textStyle.fontFamily || 'sans-serif'].join(' ');\n    textStyle.font = font;\n    var rect = text.getBoundingRect();\n    this._textX += rect.width;\n    parentGroup.add(text);\n    return text;\n  };\n  SVGParser.internalField = function () {\n    nodeParsers = {\n      'g': function (xmlNode, parentGroup) {\n        var g = new Group();\n        inheritStyle(parentGroup, g);\n        parseAttributes(xmlNode, g, this._defsUsePending, false, false);\n        return g;\n      },\n      'rect': function (xmlNode, parentGroup) {\n        var rect = new Rect();\n        inheritStyle(parentGroup, rect);\n        parseAttributes(xmlNode, rect, this._defsUsePending, false, false);\n        rect.setShape({\n          x: parseFloat(xmlNode.getAttribute('x') || '0'),\n          y: parseFloat(xmlNode.getAttribute('y') || '0'),\n          width: parseFloat(xmlNode.getAttribute('width') || '0'),\n          height: parseFloat(xmlNode.getAttribute('height') || '0')\n        });\n        rect.silent = true;\n        return rect;\n      },\n      'circle': function (xmlNode, parentGroup) {\n        var circle = new Circle();\n        inheritStyle(parentGroup, circle);\n        parseAttributes(xmlNode, circle, this._defsUsePending, false, false);\n        circle.setShape({\n          cx: parseFloat(xmlNode.getAttribute('cx') || '0'),\n          cy: parseFloat(xmlNode.getAttribute('cy') || '0'),\n          r: parseFloat(xmlNode.getAttribute('r') || '0')\n        });\n        circle.silent = true;\n        return circle;\n      },\n      'line': function (xmlNode, parentGroup) {\n        var line = new Line();\n        inheritStyle(parentGroup, line);\n        parseAttributes(xmlNode, line, this._defsUsePending, false, false);\n        line.setShape({\n          x1: parseFloat(xmlNode.getAttribute('x1') || '0'),\n          y1: parseFloat(xmlNode.getAttribute('y1') || '0'),\n          x2: parseFloat(xmlNode.getAttribute('x2') || '0'),\n          y2: parseFloat(xmlNode.getAttribute('y2') || '0')\n        });\n        line.silent = true;\n        return line;\n      },\n      'ellipse': function (xmlNode, parentGroup) {\n        var ellipse = new Ellipse();\n        inheritStyle(parentGroup, ellipse);\n        parseAttributes(xmlNode, ellipse, this._defsUsePending, false, false);\n        ellipse.setShape({\n          cx: parseFloat(xmlNode.getAttribute('cx') || '0'),\n          cy: parseFloat(xmlNode.getAttribute('cy') || '0'),\n          rx: parseFloat(xmlNode.getAttribute('rx') || '0'),\n          ry: parseFloat(xmlNode.getAttribute('ry') || '0')\n        });\n        ellipse.silent = true;\n        return ellipse;\n      },\n      'polygon': function (xmlNode, parentGroup) {\n        var pointsStr = xmlNode.getAttribute('points');\n        var pointsArr;\n        if (pointsStr) {\n          pointsArr = parsePoints(pointsStr);\n        }\n        var polygon = new Polygon({\n          shape: {\n            points: pointsArr || []\n          },\n          silent: true\n        });\n        inheritStyle(parentGroup, polygon);\n        parseAttributes(xmlNode, polygon, this._defsUsePending, false, false);\n        return polygon;\n      },\n      'polyline': function (xmlNode, parentGroup) {\n        var pointsStr = xmlNode.getAttribute('points');\n        var pointsArr;\n        if (pointsStr) {\n          pointsArr = parsePoints(pointsStr);\n        }\n        var polyline = new Polyline({\n          shape: {\n            points: pointsArr || []\n          },\n          silent: true\n        });\n        inheritStyle(parentGroup, polyline);\n        parseAttributes(xmlNode, polyline, this._defsUsePending, false, false);\n        return polyline;\n      },\n      'image': function (xmlNode, parentGroup) {\n        var img = new ZRImage();\n        inheritStyle(parentGroup, img);\n        parseAttributes(xmlNode, img, this._defsUsePending, false, false);\n        img.setStyle({\n          image: xmlNode.getAttribute('xlink:href') || xmlNode.getAttribute('href'),\n          x: +xmlNode.getAttribute('x'),\n          y: +xmlNode.getAttribute('y'),\n          width: +xmlNode.getAttribute('width'),\n          height: +xmlNode.getAttribute('height')\n        });\n        img.silent = true;\n        return img;\n      },\n      'text': function (xmlNode, parentGroup) {\n        var x = xmlNode.getAttribute('x') || '0';\n        var y = xmlNode.getAttribute('y') || '0';\n        var dx = xmlNode.getAttribute('dx') || '0';\n        var dy = xmlNode.getAttribute('dy') || '0';\n        this._textX = parseFloat(x) + parseFloat(dx);\n        this._textY = parseFloat(y) + parseFloat(dy);\n        var g = new Group();\n        inheritStyle(parentGroup, g);\n        parseAttributes(xmlNode, g, this._defsUsePending, false, true);\n        return g;\n      },\n      'tspan': function (xmlNode, parentGroup) {\n        var x = xmlNode.getAttribute('x');\n        var y = xmlNode.getAttribute('y');\n        if (x != null) {\n          this._textX = parseFloat(x);\n        }\n        if (y != null) {\n          this._textY = parseFloat(y);\n        }\n        var dx = xmlNode.getAttribute('dx') || '0';\n        var dy = xmlNode.getAttribute('dy') || '0';\n        var g = new Group();\n        inheritStyle(parentGroup, g);\n        parseAttributes(xmlNode, g, this._defsUsePending, false, true);\n        this._textX += parseFloat(dx);\n        this._textY += parseFloat(dy);\n        return g;\n      },\n      'path': function (xmlNode, parentGroup) {\n        var d = xmlNode.getAttribute('d') || '';\n        var path = createFromString(d);\n        inheritStyle(parentGroup, path);\n        parseAttributes(xmlNode, path, this._defsUsePending, false, false);\n        path.silent = true;\n        return path;\n      }\n    };\n  }();\n  return SVGParser;\n}();\nvar paintServerParsers = {\n  'lineargradient': function (xmlNode) {\n    var x1 = parseInt(xmlNode.getAttribute('x1') || '0', 10);\n    var y1 = parseInt(xmlNode.getAttribute('y1') || '0', 10);\n    var x2 = parseInt(xmlNode.getAttribute('x2') || '10', 10);\n    var y2 = parseInt(xmlNode.getAttribute('y2') || '0', 10);\n    var gradient = new LinearGradient(x1, y1, x2, y2);\n    parsePaintServerUnit(xmlNode, gradient);\n    parseGradientColorStops(xmlNode, gradient);\n    return gradient;\n  },\n  'radialgradient': function (xmlNode) {\n    var cx = parseInt(xmlNode.getAttribute('cx') || '0', 10);\n    var cy = parseInt(xmlNode.getAttribute('cy') || '0', 10);\n    var r = parseInt(xmlNode.getAttribute('r') || '0', 10);\n    var gradient = new RadialGradient(cx, cy, r);\n    parsePaintServerUnit(xmlNode, gradient);\n    parseGradientColorStops(xmlNode, gradient);\n    return gradient;\n  }\n};\nfunction parsePaintServerUnit(xmlNode, gradient) {\n  var gradientUnits = xmlNode.getAttribute('gradientUnits');\n  if (gradientUnits === 'userSpaceOnUse') {\n    gradient.global = true;\n  }\n}\nfunction parseGradientColorStops(xmlNode, gradient) {\n  var stop = xmlNode.firstChild;\n  while (stop) {\n    if (stop.nodeType === 1 && stop.nodeName.toLocaleLowerCase() === 'stop') {\n      var offsetStr = stop.getAttribute('offset');\n      var offset = void 0;\n      if (offsetStr && offsetStr.indexOf('%') > 0) {\n        offset = parseInt(offsetStr, 10) / 100;\n      } else if (offsetStr) {\n        offset = parseFloat(offsetStr);\n      } else {\n        offset = 0;\n      }\n      var styleVals = {};\n      parseInlineStyle(stop, styleVals, styleVals);\n      var stopColor = styleVals.stopColor || stop.getAttribute('stop-color') || '#000000';\n      gradient.colorStops.push({\n        offset: offset,\n        color: stopColor\n      });\n    }\n    stop = stop.nextSibling;\n  }\n}\nfunction inheritStyle(parent, child) {\n  if (parent && parent.__inheritedStyle) {\n    if (!child.__inheritedStyle) {\n      child.__inheritedStyle = {};\n    }\n    defaults(child.__inheritedStyle, parent.__inheritedStyle);\n  }\n}\nfunction parsePoints(pointsString) {\n  var list = splitNumberSequence(pointsString);\n  var points = [];\n  for (var i = 0; i < list.length; i += 2) {\n    var x = parseFloat(list[i]);\n    var y = parseFloat(list[i + 1]);\n    points.push([x, y]);\n  }\n  return points;\n}\nfunction parseAttributes(xmlNode, el, defsUsePending, onlyInlineStyle, isTextGroup) {\n  var disp = el;\n  var inheritedStyle = disp.__inheritedStyle = disp.__inheritedStyle || {};\n  var selfStyle = {};\n  if (xmlNode.nodeType === 1) {\n    parseTransformAttribute(xmlNode, el);\n    parseInlineStyle(xmlNode, inheritedStyle, selfStyle);\n    if (!onlyInlineStyle) {\n      parseAttributeStyle(xmlNode, inheritedStyle, selfStyle);\n    }\n  }\n  disp.style = disp.style || {};\n  if (inheritedStyle.fill != null) {\n    disp.style.fill = getFillStrokeStyle(disp, 'fill', inheritedStyle.fill, defsUsePending);\n  }\n  if (inheritedStyle.stroke != null) {\n    disp.style.stroke = getFillStrokeStyle(disp, 'stroke', inheritedStyle.stroke, defsUsePending);\n  }\n  each(['lineWidth', 'opacity', 'fillOpacity', 'strokeOpacity', 'miterLimit', 'fontSize'], function (propName) {\n    if (inheritedStyle[propName] != null) {\n      disp.style[propName] = parseFloat(inheritedStyle[propName]);\n    }\n  });\n  each(['lineDashOffset', 'lineCap', 'lineJoin', 'fontWeight', 'fontFamily', 'fontStyle', 'textAlign'], function (propName) {\n    if (inheritedStyle[propName] != null) {\n      disp.style[propName] = inheritedStyle[propName];\n    }\n  });\n  if (isTextGroup) {\n    disp.__selfStyle = selfStyle;\n  }\n  if (inheritedStyle.lineDash) {\n    disp.style.lineDash = map(splitNumberSequence(inheritedStyle.lineDash), function (str) {\n      return parseFloat(str);\n    });\n  }\n  if (inheritedStyle.visibility === 'hidden' || inheritedStyle.visibility === 'collapse') {\n    disp.invisible = true;\n  }\n  if (inheritedStyle.display === 'none') {\n    disp.ignore = true;\n  }\n}\nfunction applyTextAlignment(text, parentGroup) {\n  var parentSelfStyle = parentGroup.__selfStyle;\n  if (parentSelfStyle) {\n    var textBaseline = parentSelfStyle.textBaseline;\n    var zrTextBaseline = textBaseline;\n    if (!textBaseline || textBaseline === 'auto') {\n      zrTextBaseline = 'alphabetic';\n    } else if (textBaseline === 'baseline') {\n      zrTextBaseline = 'alphabetic';\n    } else if (textBaseline === 'before-edge' || textBaseline === 'text-before-edge') {\n      zrTextBaseline = 'top';\n    } else if (textBaseline === 'after-edge' || textBaseline === 'text-after-edge') {\n      zrTextBaseline = 'bottom';\n    } else if (textBaseline === 'central' || textBaseline === 'mathematical') {\n      zrTextBaseline = 'middle';\n    }\n    text.style.textBaseline = zrTextBaseline;\n  }\n  var parentInheritedStyle = parentGroup.__inheritedStyle;\n  if (parentInheritedStyle) {\n    var textAlign = parentInheritedStyle.textAlign;\n    var zrTextAlign = textAlign;\n    if (textAlign) {\n      if (textAlign === 'middle') {\n        zrTextAlign = 'center';\n      }\n      text.style.textAlign = zrTextAlign;\n    }\n  }\n}\nvar urlRegex = /^url\\(\\s*#(.*?)\\)/;\nfunction getFillStrokeStyle(el, method, str, defsUsePending) {\n  var urlMatch = str && str.match(urlRegex);\n  if (urlMatch) {\n    var url = trim(urlMatch[1]);\n    defsUsePending.push([el, method, url]);\n    return;\n  }\n  if (str === 'none') {\n    str = null;\n  }\n  return str;\n}\nfunction applyDefs(defs, defsUsePending) {\n  for (var i = 0; i < defsUsePending.length; i++) {\n    var item = defsUsePending[i];\n    item[0].style[item[1]] = defs[item[2]];\n  }\n}\nvar numberReg = /-?([0-9]*\\.)?[0-9]+([eE]-?[0-9]+)?/g;\nfunction splitNumberSequence(rawStr) {\n  return rawStr.match(numberReg) || [];\n}\nvar transformRegex = /(translate|scale|rotate|skewX|skewY|matrix)\\(([\\-\\s0-9\\.eE,]*)\\)/g;\nvar DEGREE_TO_ANGLE = Math.PI / 180;\nfunction parseTransformAttribute(xmlNode, node) {\n  var transform = xmlNode.getAttribute('transform');\n  if (transform) {\n    transform = transform.replace(/,/g, ' ');\n    var transformOps_1 = [];\n    var mt = null;\n    transform.replace(transformRegex, function (str, type, value) {\n      transformOps_1.push(type, value);\n      return '';\n    });\n    for (var i = transformOps_1.length - 1; i > 0; i -= 2) {\n      var value = transformOps_1[i];\n      var type = transformOps_1[i - 1];\n      var valueArr = splitNumberSequence(value);\n      mt = mt || matrix.create();\n      switch (type) {\n        case 'translate':\n          matrix.translate(mt, mt, [parseFloat(valueArr[0]), parseFloat(valueArr[1] || '0')]);\n          break;\n        case 'scale':\n          matrix.scale(mt, mt, [parseFloat(valueArr[0]), parseFloat(valueArr[1] || valueArr[0])]);\n          break;\n        case 'rotate':\n          matrix.rotate(mt, mt, -parseFloat(valueArr[0]) * DEGREE_TO_ANGLE, [parseFloat(valueArr[1] || '0'), parseFloat(valueArr[2] || '0')]);\n          break;\n        case 'skewX':\n          var sx = Math.tan(parseFloat(valueArr[0]) * DEGREE_TO_ANGLE);\n          matrix.mul(mt, [1, 0, sx, 1, 0, 0], mt);\n          break;\n        case 'skewY':\n          var sy = Math.tan(parseFloat(valueArr[0]) * DEGREE_TO_ANGLE);\n          matrix.mul(mt, [1, sy, 0, 1, 0, 0], mt);\n          break;\n        case 'matrix':\n          mt[0] = parseFloat(valueArr[0]);\n          mt[1] = parseFloat(valueArr[1]);\n          mt[2] = parseFloat(valueArr[2]);\n          mt[3] = parseFloat(valueArr[3]);\n          mt[4] = parseFloat(valueArr[4]);\n          mt[5] = parseFloat(valueArr[5]);\n          break;\n      }\n    }\n    node.setLocalTransform(mt);\n  }\n}\nvar styleRegex = /([^\\s:;]+)\\s*:\\s*([^:;]+)/g;\nfunction parseInlineStyle(xmlNode, inheritableStyleResult, selfStyleResult) {\n  var style = xmlNode.getAttribute('style');\n  if (!style) {\n    return;\n  }\n  styleRegex.lastIndex = 0;\n  var styleRegResult;\n  while ((styleRegResult = styleRegex.exec(style)) != null) {\n    var svgStlAttr = styleRegResult[1];\n    var zrInheritableStlAttr = hasOwn(INHERITABLE_STYLE_ATTRIBUTES_MAP, svgStlAttr) ? INHERITABLE_STYLE_ATTRIBUTES_MAP[svgStlAttr] : null;\n    if (zrInheritableStlAttr) {\n      inheritableStyleResult[zrInheritableStlAttr] = styleRegResult[2];\n    }\n    var zrSelfStlAttr = hasOwn(SELF_STYLE_ATTRIBUTES_MAP, svgStlAttr) ? SELF_STYLE_ATTRIBUTES_MAP[svgStlAttr] : null;\n    if (zrSelfStlAttr) {\n      selfStyleResult[zrSelfStlAttr] = styleRegResult[2];\n    }\n  }\n}\nfunction parseAttributeStyle(xmlNode, inheritableStyleResult, selfStyleResult) {\n  for (var i = 0; i < INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS.length; i++) {\n    var svgAttrName = INHERITABLE_STYLE_ATTRIBUTES_MAP_KEYS[i];\n    var attrValue = xmlNode.getAttribute(svgAttrName);\n    if (attrValue != null) {\n      inheritableStyleResult[INHERITABLE_STYLE_ATTRIBUTES_MAP[svgAttrName]] = attrValue;\n    }\n  }\n  for (var i = 0; i < SELF_STYLE_ATTRIBUTES_MAP_KEYS.length; i++) {\n    var svgAttrName = SELF_STYLE_ATTRIBUTES_MAP_KEYS[i];\n    var attrValue = xmlNode.getAttribute(svgAttrName);\n    if (attrValue != null) {\n      selfStyleResult[SELF_STYLE_ATTRIBUTES_MAP[svgAttrName]] = attrValue;\n    }\n  }\n}\nexport function makeViewBoxTransform(viewBoxRect, boundingRect) {\n  var scaleX = boundingRect.width / viewBoxRect.width;\n  var scaleY = boundingRect.height / viewBoxRect.height;\n  var scale = Math.min(scaleX, scaleY);\n  return {\n    scale: scale,\n    x: -(viewBoxRect.x + viewBoxRect.width / 2) * scale + (boundingRect.x + boundingRect.width / 2),\n    y: -(viewBoxRect.y + viewBoxRect.height / 2) * scale + (boundingRect.y + boundingRect.height / 2)\n  };\n}\nexport function parseSVG(xml, opt) {\n  var parser = new SVGParser();\n  return parser.parse(xml, opt);\n}\nexport { parseXML };", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}