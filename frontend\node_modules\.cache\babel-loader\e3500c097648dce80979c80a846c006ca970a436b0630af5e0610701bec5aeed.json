{"ast": null, "code": "export function createTextNode(text) {\n  return document.createTextNode(text);\n}\nexport function createComment(text) {\n  return document.createComment(text);\n}\nexport function insertBefore(parentNode, newNode, referenceNode) {\n  parentNode.insertBefore(newNode, referenceNode);\n}\nexport function removeChild(node, child) {\n  node.removeChild(child);\n}\nexport function appendChild(node, child) {\n  node.appendChild(child);\n}\nexport function parentNode(node) {\n  return node.parentNode;\n}\nexport function nextSibling(node) {\n  return node.nextSibling;\n}\nexport function tagName(elm) {\n  return elm.tagName;\n}\nexport function setTextContent(node, text) {\n  node.textContent = text;\n}\nexport function getTextContent(node) {\n  return node.textContent;\n}\nexport function isElement(node) {\n  return node.nodeType === 1;\n}\nexport function isText(node) {\n  return node.nodeType === 3;\n}\nexport function isComment(node) {\n  return node.nodeType === 8;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}