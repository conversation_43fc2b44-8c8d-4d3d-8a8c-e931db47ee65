{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/esm/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/esm/createClass\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\n// Firefox has low performance of map.\nvar CacheMap = /*#__PURE__*/function () {\n  function CacheMap() {\n    _classCallCheck(this, CacheMap);\n    _defineProperty(this, \"maps\", void 0);\n    // Used for cache key\n    // `useMemo` no need to update if `id` not change\n    _defineProperty(this, \"id\", 0);\n    _defineProperty(this, \"diffRecords\", new Map());\n    this.maps = Object.create(null);\n  }\n  _createClass(CacheMap, [{\n    key: \"set\",\n    value: function set(key, value) {\n      // Record prev value\n      this.diffRecords.set(key, this.maps[key]);\n      this.maps[key] = value;\n      this.id += 1;\n    }\n  }, {\n    key: \"get\",\n    value: function get(key) {\n      return this.maps[key];\n    }\n\n    /**\n     * CacheMap will record the key changed.\n     * To help to know what's update in the next render.\n     */\n  }, {\n    key: \"resetRecord\",\n    value: function resetRecord() {\n      this.diffRecords.clear();\n    }\n  }, {\n    key: \"getRecord\",\n    value: function getRecord() {\n      return this.diffRecords;\n    }\n  }]);\n  return CacheMap;\n}();\nexport default CacheMap;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}