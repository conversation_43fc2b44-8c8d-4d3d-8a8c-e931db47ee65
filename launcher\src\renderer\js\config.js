// 配置页面逻辑
class ConfigPage {
    constructor() {
        this.config = null;
        this.isLoading = false;
        this.validationResults = {
            tushare: { valid: false, testing: false },
            wind: { valid: false, testing: false },
            ricequant: { valid: false, testing: false }
        };
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadConfiguration();
    }

    initializeElements() {
        // 表单元素
        this.elements = {
            // 基础元素
            backBtn: document.getElementById('back-btn'),
            saveBtn: document.getElementById('save-btn'),
            resetBtn: document.getElementById('reset-btn'),
            cancelBtn: document.getElementById('cancel-btn'),
            loading: document.getElementById('loading'),
            loadingText: document.getElementById('loading-text'),
            alert: document.getElementById('alert'),
            alertContent: document.getElementById('alert-content'),

            // Tushare
            tushareToken: document.getElementById('tushare-token'),
            testTushare: document.getElementById('test-tushare'),
            tushareStatus: document.getElementById('tushare-status'),
            tushareStatusText: document.getElementById('tushare-status-text'),
            tushareLink: document.getElementById('tushare-link'),

            // Wind
            windEnabled: document.getElementById('wind-enabled'),
            windConfig: document.getElementById('wind-config'),
            windUsername: document.getElementById('wind-username'),
            windPassword: document.getElementById('wind-password'),
            testWind: document.getElementById('test-wind'),
            windStatus: document.getElementById('wind-status'),
            windStatusText: document.getElementById('wind-status-text'),

            // 米筐
            ricequantEnabled: document.getElementById('ricequant-enabled'),
            ricequantConfig: document.getElementById('ricequant-config'),
            ricequantKey: document.getElementById('ricequant-key'),
            testRicequant: document.getElementById('test-ricequant'),
            ricequantStatus: document.getElementById('ricequant-status'),
            ricequantStatusText: document.getElementById('ricequant-status-text'),
            ricequantLink: document.getElementById('ricequant-link'),

            // DeepSeek
            deepseekKey: document.getElementById('deepseek-key'),
            deepseekLink: document.getElementById('deepseek-link')
        };
    }

    setupEventListeners() {
        // 导航按钮
        this.elements.backBtn.addEventListener('click', () => this.goBack());
        this.elements.cancelBtn.addEventListener('click', () => this.goBack());
        
        // 操作按钮
        this.elements.saveBtn.addEventListener('click', () => this.saveConfiguration());
        this.elements.resetBtn.addEventListener('click', () => this.resetConfiguration());

        // 测试按钮
        this.elements.testTushare.addEventListener('click', () => this.testToken('tushare'));
        this.elements.testWind.addEventListener('click', () => this.testToken('wind'));
        this.elements.testRicequant.addEventListener('click', () => this.testToken('ricequant'));

        // 可选配置切换
        this.elements.windEnabled.addEventListener('change', (e) => {
            this.toggleOptionalConfig('wind', e.target.checked);
        });
        
        this.elements.ricequantEnabled.addEventListener('change', (e) => {
            this.toggleOptionalConfig('ricequant', e.target.checked);
        });

        // 外部链接
        this.elements.tushareLink.addEventListener('click', (e) => {
            e.preventDefault();
            window.electronAPI.openExternal('https://tushare.pro/');
        });

        this.elements.ricequantLink.addEventListener('click', (e) => {
            e.preventDefault();
            window.electronAPI.openExternal('https://www.ricequant.com/');
        });

        this.elements.deepseekLink.addEventListener('click', (e) => {
            e.preventDefault();
            window.electronAPI.openExternal('https://platform.deepseek.com/');
        });

        // 输入框变化监听
        this.elements.tushareToken.addEventListener('input', () => {
            this.resetValidationStatus('tushare');
        });

        this.elements.windUsername.addEventListener('input', () => {
            this.resetValidationStatus('wind');
        });

        this.elements.windPassword.addEventListener('input', () => {
            this.resetValidationStatus('wind');
        });

        this.elements.ricequantKey.addEventListener('input', () => {
            this.resetValidationStatus('ricequant');
        });
    }

    async loadConfiguration() {
        try {
            this.showLoading('正在加载配置...');
            
            this.config = await window.electronAPI.loadConfig();
            this.populateForm();
            this.updateValidationStatus();
            
            this.hideLoading();
            this.showAlert('配置加载成功', 'success');
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(`加载配置失败: ${error.message}`, 'error');
            console.error('加载配置失败:', error);
        }
    }

    populateForm() {
        if (!this.config) return;

        // Tushare配置
        this.elements.tushareToken.value = this.config.tushare.token || '';

        // Wind配置
        this.elements.windEnabled.checked = this.config.wind.enabled || false;
        this.elements.windUsername.value = this.config.wind.username || '';
        this.elements.windPassword.value = this.config.wind.password || '';
        this.toggleOptionalConfig('wind', this.config.wind.enabled);

        // 米筐配置
        this.elements.ricequantEnabled.checked = this.config.ricequant.enabled || false;
        this.elements.ricequantKey.value = this.config.ricequant.api_key || '';
        this.toggleOptionalConfig('ricequant', this.config.ricequant.enabled);

        // DeepSeek配置
        this.elements.deepseekKey.value = this.config.deepseek.api_key || '';
    }

    updateValidationStatus() {
        if (!this.config) return;

        // 更新Tushare状态
        this.updateStatusIndicator('tushare', this.config.tushare.status || 'unconfigured');
        
        // 更新Wind状态
        this.updateStatusIndicator('wind', this.config.wind.status || 'unconfigured');
        
        // 更新米筐状态
        this.updateStatusIndicator('ricequant', this.config.ricequant.status || 'unconfigured');
    }

    updateStatusIndicator(source, status) {
        const statusDot = this.elements[`${source}Status`];
        const statusText = this.elements[`${source}StatusText`];

        statusDot.className = 'status-dot';
        
        switch (status) {
            case 'valid':
                statusDot.classList.add('valid');
                statusText.textContent = '已验证';
                this.validationResults[source].valid = true;
                break;
            case 'invalid':
                statusText.textContent = '验证失败';
                this.validationResults[source].valid = false;
                break;
            case 'testing':
                statusDot.classList.add('testing');
                statusText.textContent = '验证中...';
                this.validationResults[source].testing = true;
                break;
            default:
                statusText.textContent = '未配置';
                this.validationResults[source].valid = false;
        }
    }

    async testToken(source) {
        try {
            this.updateStatusIndicator(source, 'testing');
            
            let token;
            switch (source) {
                case 'tushare':
                    token = this.elements.tushareToken.value.trim();
                    if (!token) {
                        throw new Error('请输入Tushare Token');
                    }
                    break;
                case 'wind':
                    token = {
                        username: this.elements.windUsername.value.trim(),
                        password: this.elements.windPassword.value.trim()
                    };
                    if (!token.username || !token.password) {
                        throw new Error('请输入Wind用户名和密码');
                    }
                    break;
                case 'ricequant':
                    token = this.elements.ricequantKey.value.trim();
                    if (!token) {
                        throw new Error('请输入米筐API Key');
                    }
                    break;
            }

            const result = await window.electronAPI.validateToken(source, token);
            
            if (result.valid) {
                this.updateStatusIndicator(source, 'valid');
                this.showAlert(`${this.getSourceName(source)}验证成功`, 'success');
            } else {
                this.updateStatusIndicator(source, 'invalid');
                this.showAlert(`${this.getSourceName(source)}验证失败: ${result.error}`, 'error');
            }
            
        } catch (error) {
            this.updateStatusIndicator(source, 'invalid');
            this.showAlert(`验证失败: ${error.message}`, 'error');
            console.error(`${source}验证失败:`, error);
        }
    }

    getSourceName(source) {
        const names = {
            tushare: 'Tushare',
            wind: 'Wind',
            ricequant: '米筐'
        };
        return names[source] || source;
    }

    resetValidationStatus(source) {
        this.updateStatusIndicator(source, 'unconfigured');
        this.validationResults[source].valid = false;
        this.validationResults[source].testing = false;
    }

    toggleOptionalConfig(source, enabled) {
        const configElement = this.elements[`${source}Config`];
        if (enabled) {
            configElement.classList.add('enabled');
        } else {
            configElement.classList.remove('enabled');
            this.resetValidationStatus(source);
        }
    }

    async saveConfiguration() {
        try {
            this.showLoading('正在保存配置...');

            // 收集表单数据
            const newConfig = {
                ...this.config,
                tushare: {
                    ...this.config.tushare,
                    token: this.elements.tushareToken.value.trim(),
                    status: this.validationResults.tushare.valid ? 'valid' : 'unconfigured'
                },
                wind: {
                    ...this.config.wind,
                    enabled: this.elements.windEnabled.checked,
                    username: this.elements.windUsername.value.trim(),
                    password: this.elements.windPassword.value.trim(),
                    status: this.validationResults.wind.valid ? 'valid' : 'unconfigured'
                },
                ricequant: {
                    ...this.config.ricequant,
                    enabled: this.elements.ricequantEnabled.checked,
                    api_key: this.elements.ricequantKey.value.trim(),
                    status: this.validationResults.ricequant.valid ? 'valid' : 'unconfigured'
                },
                deepseek: {
                    ...this.config.deepseek,
                    api_key: this.elements.deepseekKey.value.trim()
                },
                system: {
                    ...this.config.system,
                    first_run: false
                }
            };

            // 保存配置
            const success = await window.electronAPI.saveConfig(newConfig);
            
            if (success) {
                this.config = newConfig;
                this.hideLoading();
                this.showAlert('配置保存成功！', 'success');
                
                // 2秒后返回主页面
                setTimeout(() => {
                    this.goBack();
                }, 2000);
            } else {
                throw new Error('保存失败');
            }
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(`保存配置失败: ${error.message}`, 'error');
            console.error('保存配置失败:', error);
        }
    }

    async resetConfiguration() {
        try {
            const confirmed = await window.electronAPI.showMessage({
                type: 'question',
                buttons: ['确定', '取消'],
                defaultId: 1,
                title: '确认重置',
                message: '确定要重置所有配置吗？',
                detail: '这将清除所有已保存的配置信息。'
            });

            if (confirmed.response === 0) {
                this.showLoading('正在重置配置...');
                
                // 重置表单
                this.elements.tushareToken.value = '';
                this.elements.windEnabled.checked = false;
                this.elements.windUsername.value = '';
                this.elements.windPassword.value = '';
                this.elements.ricequantEnabled.checked = false;
                this.elements.ricequantKey.value = '';
                this.elements.deepseekKey.value = '***********************************';

                // 重置状态
                this.resetValidationStatus('tushare');
                this.resetValidationStatus('wind');
                this.resetValidationStatus('ricequant');
                this.toggleOptionalConfig('wind', false);
                this.toggleOptionalConfig('ricequant', false);

                this.hideLoading();
                this.showAlert('配置已重置', 'success');
            }
            
        } catch (error) {
            this.hideLoading();
            this.showAlert(`重置失败: ${error.message}`, 'error');
            console.error('重置配置失败:', error);
        }
    }

    goBack() {
        window.electronAPI.navigateTo('index');
    }

    showLoading(text) {
        this.elements.loadingText.textContent = text;
        this.elements.loading.style.display = 'block';
        this.isLoading = true;
    }

    hideLoading() {
        this.elements.loading.style.display = 'none';
        this.isLoading = false;
    }

    showAlert(message, type = 'info') {
        this.elements.alertContent.textContent = message;
        this.elements.alert.className = `alert ${type}`;
        this.elements.alert.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            this.elements.alert.style.display = 'none';
        }, 3000);
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new ConfigPage();
});
