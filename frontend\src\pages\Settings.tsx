import React, { useState, useEffect } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Space,
  Typography,
  Alert,
  Divider,
  Switch,
  message,
  Modal,
  Tag,
  Row,
  Col,
  Spin
} from 'antd';
import {
  SettingOutlined,
  KeyOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  ReloadOutlined,
  SaveOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone
} from '@ant-design/icons';
import axios from 'axios';

const { Title, Text, Paragraph } = Typography;
const { confirm } = Modal;

interface TokenConfig {
  tushare_token: string;
  deepseek_api_key: string;
  enabled_features: {
    ai_analysis: boolean;
    realtime_data: boolean;
    notifications: boolean;
  };
}

interface TokenStatus {
  tushare: 'valid' | 'invalid' | 'unconfigured' | 'testing';
  deepseek: 'valid' | 'invalid' | 'unconfigured' | 'testing';
}

const Settings: React.FC = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [config, setConfig] = useState<TokenConfig>({
    tushare_token: '',
    deepseek_api_key: '',
    enabled_features: {
      ai_analysis: true,
      realtime_data: true,
      notifications: true
    }
  });
  const [tokenStatus, setTokenStatus] = useState<TokenStatus>({
    tushare: 'unconfigured',
    deepseek: 'unconfigured'
  });

  useEffect(() => {
    // 暂时使用本地存储，避免API调用问题
    loadLocalConfiguration();
  }, []);

  const loadLocalConfiguration = () => {
    try {
      const savedConfig = localStorage.getItem('quantTradingConfig');
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig);
        setConfig(parsedConfig);
        form.setFieldsValue(parsedConfig);

        // 检查token状态
        if (parsedConfig.tushare_token) {
          setTokenStatus(prev => ({ ...prev, tushare: 'valid' }));
        }
        if (parsedConfig.deepseek_api_key) {
          setTokenStatus(prev => ({ ...prev, deepseek: 'valid' }));
        }
      }
    } catch (error) {
      console.error('加载本地配置失败:', error);
      message.warning('加载本地配置失败，将使用默认配置');
    }
  };

  const testToken = async (tokenType: 'tushare' | 'deepseek') => {
    const values = form.getFieldsValue();
    const token = tokenType === 'tushare' ? values.tushare_token : values.deepseek_api_key;

    if (!token) {
      message.warning(`请先输入${tokenType === 'tushare' ? 'Tushare' : 'DeepSeek'} Token`);
      return;
    }

    // 暂时使用简单的格式验证，避免API调用问题
    setTokenStatus(prev => ({ ...prev, [tokenType]: 'testing' }));

    setTimeout(() => {
      if (tokenType === 'tushare') {
        // Tushare token通常是32位以上的字符串
        if (token.length >= 32) {
          setTokenStatus(prev => ({ ...prev, [tokenType]: 'valid' }));
          message.success('Tushare Token格式验证通过');
        } else {
          setTokenStatus(prev => ({ ...prev, [tokenType]: 'invalid' }));
          message.error('Tushare Token长度应至少32位');
        }
      } else {
        // DeepSeek API key应以sk-开头
        if (token.startsWith('sk-') && token.length > 10) {
          setTokenStatus(prev => ({ ...prev, [tokenType]: 'valid' }));
          message.success('DeepSeek API Key格式验证通过');
        } else {
          setTokenStatus(prev => ({ ...prev, [tokenType]: 'invalid' }));
          message.error('DeepSeek API Key应以sk-开头');
        }
      }
    }, 1000);
  };

  const saveConfiguration = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);

      // 暂时保存到本地存储
      localStorage.setItem('quantTradingConfig', JSON.stringify(values));
      setConfig(values);

      message.success('配置已保存到本地存储');
      message.info('注意：配置已保存到浏览器本地存储，如需持久化请手动备份');

    } catch (error) {
      console.error('保存配置失败:', error);
      message.error('保存配置失败');
    } finally {
      setLoading(false);
    }
  };

  const resetConfiguration = () => {
    confirm({
      title: '确认重置配置',
      icon: <ExclamationCircleOutlined />,
      content: '这将清除所有已保存的Token配置，确定要继续吗？',
      okText: '确定',
      cancelText: '取消',
      onOk() {
        form.resetFields();
        setTokenStatus({
          tushare: 'unconfigured',
          deepseek: 'unconfigured'
        });
        message.success('配置已重置');
      },
    });
  };

  const getStatusTag = (status: string) => {
    switch (status) {
      case 'valid':
        return <Tag color="success" icon={<CheckCircleOutlined />}>已验证</Tag>;
      case 'invalid':
        return <Tag color="error" icon={<ExclamationCircleOutlined />}>验证失败</Tag>;
      case 'testing':
        return <Tag color="processing" icon={<Spin size="small" />}>验证中...</Tag>;
      default:
        return <Tag color="default">未配置</Tag>;
    }
  };

  return (
    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>
      <Title level={2}>
        <SettingOutlined /> 系统设置
      </Title>
      
      <Alert
        message="Token配置说明"
        description="请配置相应的API Token以启用完整功能。当前版本使用本地存储，配置仅保存在浏览器中。"
        type="info"
        showIcon
        style={{ marginBottom: '16px' }}
      />

      <Alert
        message="简化版本提示"
        description="当前为简化版本，仅提供格式验证。完整的API验证功能需要后端服务完全启动后才能使用。"
        type="warning"
        showIcon
        style={{ marginBottom: '24px' }}
      />

      <Spin spinning={loading}>
        <Form
          form={form}
          layout="vertical"
          initialValues={config}
          onFinish={saveConfiguration}
        >
          <Row gutter={[24, 24]}>
            {/* Tushare配置 */}
            <Col xs={24} lg={12}>
              <Card
                title={
                  <Space>
                    <KeyOutlined />
                    Tushare Pro配置
                    {getStatusTag(tokenStatus.tushare)}
                  </Space>
                }
                extra={
                  <Button
                    size="small"
                    onClick={() => testToken('tushare')}
                    loading={tokenStatus.tushare === 'testing'}
                  >
                    测试连接
                  </Button>
                }
              >
                <Form.Item
                  name="tushare_token"
                  label="Tushare Token"
                  rules={[
                    { required: true, message: '请输入Tushare Token' },
                    { min: 32, message: 'Token长度至少32位' }
                  ]}
                >
                  <Input.Password
                    placeholder="请输入Tushare Pro Token"
                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                  />
                </Form.Item>
                
                <Paragraph type="secondary" style={{ fontSize: '12px' }}>
                  用于获取股票数据和行情信息。
                  <br />
                  获取方式: 访问 <a href="https://tushare.pro/" target="_blank" rel="noopener noreferrer">tushare.pro</a> 注册并获取Token
                </Paragraph>
              </Card>
            </Col>

            {/* DeepSeek配置 */}
            <Col xs={24} lg={12}>
              <Card
                title={
                  <Space>
                    <KeyOutlined />
                    DeepSeek AI配置
                    {getStatusTag(tokenStatus.deepseek)}
                  </Space>
                }
                extra={
                  <Button
                    size="small"
                    onClick={() => testToken('deepseek')}
                    loading={tokenStatus.deepseek === 'testing'}
                  >
                    测试连接
                  </Button>
                }
              >
                <Form.Item
                  name="deepseek_api_key"
                  label="DeepSeek API Key"
                  rules={[
                    { required: true, message: '请输入DeepSeek API Key' },
                    { pattern: /^sk-/, message: 'API Key应以sk-开头' }
                  ]}
                >
                  <Input.Password
                    placeholder="请输入DeepSeek API Key"
                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                  />
                </Form.Item>
                
                <Paragraph type="secondary" style={{ fontSize: '12px' }}>
                  用于AI智能分析和市场预测。
                  <br />
                  获取方式: 访问 <a href="https://platform.deepseek.com/" target="_blank" rel="noopener noreferrer">platform.deepseek.com</a> 获取API Key
                </Paragraph>
              </Card>
            </Col>
          </Row>

          <Divider />

          {/* 功能开关 */}
          <Card title="功能设置" style={{ marginTop: '24px' }}>
            <Row gutter={[24, 16]}>
              <Col xs={24} sm={8}>
                <Form.Item
                  name={['enabled_features', 'ai_analysis']}
                  label="AI智能分析"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <Text type="secondary">启用AI市场分析和预测功能</Text>
              </Col>
              
              <Col xs={24} sm={8}>
                <Form.Item
                  name={['enabled_features', 'realtime_data']}
                  label="实时数据"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <Text type="secondary">启用实时行情数据推送</Text>
              </Col>
              
              <Col xs={24} sm={8}>
                <Form.Item
                  name={['enabled_features', 'notifications']}
                  label="消息通知"
                  valuePropName="checked"
                >
                  <Switch />
                </Form.Item>
                <Text type="secondary">启用系统消息和告警通知</Text>
              </Col>
            </Row>
          </Card>

          {/* 操作按钮 */}
          <Card style={{ marginTop: '24px', textAlign: 'center' }}>
            <Space size="large">
              <Button
                type="primary"
                icon={<SaveOutlined />}
                htmlType="submit"
                loading={loading}
                size="large"
              >
                保存配置
              </Button>
              
              <Button
                icon={<ReloadOutlined />}
                onClick={loadConfiguration}
                size="large"
              >
                重新加载
              </Button>
              
              <Button
                danger
                onClick={resetConfiguration}
                size="large"
              >
                重置配置
              </Button>
            </Space>
          </Card>
        </Form>
      </Spin>
    </div>
  );
};

export default Settings;
