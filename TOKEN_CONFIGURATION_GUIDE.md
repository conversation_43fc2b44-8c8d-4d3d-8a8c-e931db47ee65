# 🔑 Token配置指南

## 📋 概述

量化交易系统提供了多种方式来配置和管理API Token，确保用户可以方便地更新和维护系统配置。

## 🎯 配置方式

### 1. 🖥️ Web界面配置（推荐）

#### 访问设置页面
- 启动系统后，访问前端界面：http://localhost:3001
- 点击左侧菜单的"系统设置"
- 或直接访问：http://localhost:3001/settings

#### 配置功能
- ✅ **Tushare Token配置**：输入和验证Tushare Pro Token
- ✅ **DeepSeek API Key配置**：输入和验证DeepSeek API密钥
- ✅ **实时Token验证**：点击"测试连接"按钮验证Token有效性
- ✅ **功能开关**：控制AI分析、实时数据、消息通知等功能
- ✅ **安全存储**：Token安全加密存储在本地配置文件中

#### 操作步骤
1. **输入Token**：在相应字段输入您的API Token
2. **测试连接**：点击"测试连接"验证Token有效性
3. **保存配置**：点击"保存配置"按钮保存设置
4. **重启生效**：某些配置可能需要重启系统生效

### 2. 🖱️ Electron启动器配置

#### 使用启动器
- 双击运行 `launcher/量化交易系统启动器.exe`
- 点击"系统配置"卡片
- 在配置界面中输入相应的Token

#### 启动器功能
- ✅ **图形化配置界面**
- ✅ **Token有效性验证**
- ✅ **一键启动系统**
- ✅ **服务状态监控**

### 3. 📝 手动配置文件

#### 配置文件位置
```
backend/config/user_config.json
```

#### 配置文件格式
```json
{
  "tushare_token": "your_tushare_token_here",
  "deepseek_api_key": "your_deepseek_api_key_here",
  "enabled_features": {
    "ai_analysis": true,
    "realtime_data": true,
    "notifications": true
  }
}
```

#### 环境变量配置
```bash
# 设置环境变量
export TUSHARE_TOKEN="your_tushare_token"
export DEEPSEEK_API_KEY="your_deepseek_api_key"
```

## 🔐 Token获取方式

### Tushare Pro Token
1. **注册账号**：访问 [https://tushare.pro/](https://tushare.pro/)
2. **实名认证**：完成实名认证流程
3. **获取Token**：在个人中心获取API Token
4. **积分要求**：某些高级数据需要积分支持

### DeepSeek API Key
1. **注册账号**：访问 [https://platform.deepseek.com/](https://platform.deepseek.com/)
2. **创建API Key**：在API管理页面创建新的API密钥
3. **充值使用**：根据使用量进行充值
4. **权限设置**：确保API Key有足够的权限

## 🛡️ 安全说明

### Token安全存储
- ✅ **本地存储**：所有Token仅存储在本地配置文件中
- ✅ **加密保护**：敏感信息在界面中部分隐藏显示
- ✅ **权限控制**：配置文件仅当前用户可访问
- ✅ **不上传云端**：Token信息不会上传到任何云端服务

### 安全建议
- 🔒 **定期更换**：建议定期更换API Token
- 🔒 **权限最小化**：仅授予必要的API权限
- 🔒 **监控使用**：定期检查API使用情况
- 🔒 **备份配置**：重要配置建议备份

## 🔧 故障排除

### 常见问题

#### 1. Token验证失败
- **检查格式**：确保Token格式正确
- **检查网络**：确保网络连接正常
- **检查权限**：确保Token有足够权限
- **检查余额**：确保API账户有足够余额

#### 2. 配置不生效
- **重启系统**：某些配置需要重启后生效
- **清除缓存**：清除浏览器缓存后重试
- **检查文件权限**：确保配置文件可读写

#### 3. 界面无法访问
- **检查端口**：确保3001端口未被占用
- **检查防火墙**：确保防火墙允许访问
- **检查服务状态**：确保前后端服务正常运行

## 📞 技术支持

### 获取帮助
- 📧 **邮件支持**：发送问题到技术支持邮箱
- 📱 **在线文档**：查看详细的在线文档
- 💬 **社区论坛**：在用户社区提问交流

### 日志查看
```bash
# 查看后端日志
tail -f backend/logs/backend.log

# 查看前端日志
# 在浏览器开发者工具中查看控制台日志
```

## 🚀 最佳实践

### 配置管理
1. **使用Web界面**：优先使用Web界面进行配置
2. **定期备份**：定期备份配置文件
3. **测试验证**：配置后及时测试验证
4. **文档记录**：记录配置变更历史

### 性能优化
1. **合理使用**：避免频繁调用API
2. **缓存策略**：合理使用数据缓存
3. **监控用量**：定期监控API使用量
4. **优化策略**：根据使用情况优化策略

---

**注意**：请妥善保管您的API Token，不要在公共场所或不安全的环境中输入Token信息。
