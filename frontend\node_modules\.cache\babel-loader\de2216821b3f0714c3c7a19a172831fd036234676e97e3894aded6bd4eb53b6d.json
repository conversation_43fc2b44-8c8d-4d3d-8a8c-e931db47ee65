{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport * as React from 'react';\n\n/**\n * Size info need loop query for the `heights` which will has the perf issue.\n * Let cache result for each render phase.\n */\nexport function useGetSize(mergedData, getKey, heights, itemHeight) {\n  var _React$useMemo = React.useMemo(function () {\n      return [new Map(), []];\n    }, [mergedData, heights.id, itemHeight]),\n    _React$useMemo2 = _slicedToArray(_React$useMemo, 2),\n    key2Index = _React$useMemo2[0],\n    bottomList = _React$useMemo2[1];\n  var getSize = function getSize(startKey) {\n    var endKey = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : startKey;\n    // Get from cache first\n    var startIndex = key2Index.get(startKey);\n    var endIndex = key2Index.get(endKey);\n\n    // Loop to fill the cache\n    if (startIndex === undefined || endIndex === undefined) {\n      var dataLen = mergedData.length;\n      for (var i = bottomList.length; i < dataLen; i += 1) {\n        var _heights$get;\n        var item = mergedData[i];\n        var key = getKey(item);\n        key2Index.set(key, i);\n        var cacheHeight = (_heights$get = heights.get(key)) !== null && _heights$get !== void 0 ? _heights$get : itemHeight;\n        bottomList[i] = (bottomList[i - 1] || 0) + cacheHeight;\n        if (key === startKey) {\n          startIndex = i;\n        }\n        if (key === endKey) {\n          endIndex = i;\n        }\n        if (startIndex !== undefined && endIndex !== undefined) {\n          break;\n        }\n      }\n    }\n    return {\n      top: bottomList[startIndex - 1] || 0,\n      bottom: bottomList[endIndex]\n    };\n  };\n  return getSize;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}