@echo off
echo ========================================
echo Manual Start - Quantitative Trading System
echo ========================================
echo.

echo Current directory: %CD%
echo.

echo Step 1: Check if we are in the correct directory
if exist "backend" (
    echo OK: backend directory found
) else (
    echo ERROR: backend directory not found
    echo Please make sure you are running this script from the project root directory
    pause
    exit /b 1
)

if exist "frontend" (
    echo OK: frontend directory found
) else (
    echo ERROR: frontend directory not found
    echo Please make sure you are running this script from the project root directory
    pause
    exit /b 1
)

echo.
echo Step 2: Starting backend service
echo Opening new window for backend...
start "Backend" cmd /c "echo Starting backend... && cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 && pause"

echo.
echo Step 3: Waiting 5 seconds for backend to start...
timeout /t 5 /nobreak

echo.
echo Step 4: Starting frontend service
echo Opening new window for frontend...
start "Frontend" cmd /c "echo Starting frontend... && cd frontend && npm start && pause"

echo.
echo ========================================
echo Services are starting...
echo ========================================
echo.
echo Two windows should have opened:
echo 1. Backend window (Python server)
echo 2. Frontend window (React app)
echo.
echo Once both are running, open your browser to:
echo http://localhost:3000
echo.
echo Backend API will be available at:
echo http://localhost:8000
echo.
pause
