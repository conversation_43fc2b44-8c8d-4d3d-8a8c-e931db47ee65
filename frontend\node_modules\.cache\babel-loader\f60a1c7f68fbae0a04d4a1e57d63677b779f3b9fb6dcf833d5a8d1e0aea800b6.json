{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\pages\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Form, Input, Button, Space, Typography, Alert, Divider, Switch, message, Modal, Tag, Row, Col, Spin } from 'antd';\nimport { SettingOutlined, KeyOutlined, CheckCircleOutlined, ExclamationCircleOutlined, ReloadOutlined, SaveOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';\nimport axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  confirm\n} = Modal;\nconst Settings = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [config, setConfig] = useState({\n    tushare_token: '',\n    deepseek_api_key: '',\n    enabled_features: {\n      ai_analysis: true,\n      realtime_data: true,\n      notifications: true\n    }\n  });\n  const [tokenStatus, setTokenStatus] = useState({\n    tushare: 'unconfigured',\n    deepseek: 'unconfigured'\n  });\n  useEffect(() => {\n    loadConfiguration();\n  }, []);\n  const loadConfiguration = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/v1/config/tokens');\n      if (response.data) {\n        setConfig(response.data);\n        form.setFieldsValue(response.data);\n\n        // 检查token状态\n        if (response.data.tushare_token) {\n          setTokenStatus(prev => ({\n            ...prev,\n            tushare: 'valid'\n          }));\n        }\n        if (response.data.deepseek_api_key) {\n          setTokenStatus(prev => ({\n            ...prev,\n            deepseek: 'valid'\n          }));\n        }\n      }\n    } catch (error) {\n      console.error('加载配置失败:', error);\n      message.error('加载配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const testToken = async tokenType => {\n    const values = form.getFieldsValue();\n    const token = tokenType === 'tushare' ? values.tushare_token : values.deepseek_api_key;\n    if (!token) {\n      message.warning(`请先输入${tokenType === 'tushare' ? 'Tushare' : 'DeepSeek'} Token`);\n      return;\n    }\n    try {\n      setTokenStatus(prev => ({\n        ...prev,\n        [tokenType]: 'testing'\n      }));\n      const response = await axios.post(`/api/v1/config/test-token`, {\n        token_type: tokenType,\n        token: token\n      });\n      if (response.data.valid) {\n        setTokenStatus(prev => ({\n          ...prev,\n          [tokenType]: 'valid'\n        }));\n        message.success(`${tokenType === 'tushare' ? 'Tushare' : 'DeepSeek'} Token验证成功`);\n      } else {\n        setTokenStatus(prev => ({\n          ...prev,\n          [tokenType]: 'invalid'\n        }));\n        message.error(`Token验证失败: ${response.data.error || '未知错误'}`);\n      }\n    } catch (error) {\n      setTokenStatus(prev => ({\n        ...prev,\n        [tokenType]: 'invalid'\n      }));\n      message.error('Token验证失败');\n      console.error('Token验证失败:', error);\n    }\n  };\n  const saveConfiguration = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n      const response = await axios.post('/api/v1/config/tokens', values);\n      if (response.data.success) {\n        setConfig(values);\n        message.success('配置保存成功');\n\n        // 重新检查token状态\n        if (values.tushare_token) {\n          testToken('tushare');\n        }\n        if (values.deepseek_api_key) {\n          testToken('deepseek');\n        }\n      } else {\n        message.error('配置保存失败');\n      }\n    } catch (error) {\n      console.error('保存配置失败:', error);\n      message.error('保存配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const resetConfiguration = () => {\n    confirm({\n      title: '确认重置配置',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 13\n      }, this),\n      content: '这将清除所有已保存的Token配置，确定要继续吗？',\n      okText: '确定',\n      cancelText: '取消',\n      onOk() {\n        form.resetFields();\n        setTokenStatus({\n          tushare: 'unconfigured',\n          deepseek: 'unconfigured'\n        });\n        message.success('配置已重置');\n      }\n    });\n  };\n  const getStatusTag = status => {\n    switch (status) {\n      case 'valid':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 175,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u9A8C\\u8BC1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 16\n        }, this);\n      case 'invalid':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 177,\n            columnNumber: 41\n          }, this),\n          children: \"\\u9A8C\\u8BC1\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 16\n        }, this);\n      case 'testing':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          icon: /*#__PURE__*/_jsxDEV(Spin, {\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 46\n          }, this),\n          children: \"\\u9A8C\\u8BC1\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          children: \"\\u672A\\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 181,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px',\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 188,\n        columnNumber: 9\n      }, this), \" \\u7CFB\\u7EDF\\u8BBE\\u7F6E\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 187,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Token\\u914D\\u7F6E\\u8BF4\\u660E\",\n      description: \"\\u8BF7\\u914D\\u7F6E\\u76F8\\u5E94\\u7684API Token\\u4EE5\\u542F\\u7528\\u5B8C\\u6574\\u529F\\u80FD\\u3002\\u6240\\u6709Token\\u90FD\\u4F1A\\u5B89\\u5168\\u5B58\\u50A8\\u5728\\u672C\\u5730\\u3002\",\n      type: \"info\",\n      showIcon: true,\n      style: {\n        marginBottom: '24px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 191,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: config,\n        onFinish: saveConfiguration,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(KeyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 212,\n                  columnNumber: 21\n                }, this), \"Tushare Pro\\u914D\\u7F6E\", getStatusTag(tokenStatus.tushare)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 211,\n                columnNumber: 19\n              }, this),\n              extra: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                onClick: () => testToken('tushare'),\n                loading: tokenStatus.tushare === 'testing',\n                children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 218,\n                columnNumber: 19\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tushare_token\",\n                label: \"Tushare Token\",\n                rules: [{\n                  required: true,\n                  message: '请输入Tushare Token'\n                }, {\n                  min: 32,\n                  message: 'Token长度至少32位'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165Tushare Pro Token\",\n                  iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 57\n                  }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 237,\n                    columnNumber: 74\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 235,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 227,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: [\"\\u7528\\u4E8E\\u83B7\\u53D6\\u80A1\\u7968\\u6570\\u636E\\u548C\\u884C\\u60C5\\u4FE1\\u606F\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 243,\n                  columnNumber: 19\n                }, this), \"\\u83B7\\u53D6\\u65B9\\u5F0F: \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://tushare.pro/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"tushare.pro\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 244,\n                  columnNumber: 28\n                }, this), \" \\u6CE8\\u518C\\u5E76\\u83B7\\u53D6Token\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 241,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(KeyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 254,\n                  columnNumber: 21\n                }, this), \"DeepSeek AI\\u914D\\u7F6E\", getStatusTag(tokenStatus.deepseek)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 253,\n                columnNumber: 19\n              }, this),\n              extra: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                onClick: () => testToken('deepseek'),\n                loading: tokenStatus.deepseek === 'testing',\n                children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 260,\n                columnNumber: 19\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"deepseek_api_key\",\n                label: \"DeepSeek API Key\",\n                rules: [{\n                  required: true,\n                  message: '请输入DeepSeek API Key'\n                }, {\n                  pattern: /^sk-/,\n                  message: 'API Key应以sk-开头'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165DeepSeek API Key\",\n                  iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 57\n                  }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 279,\n                    columnNumber: 74\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 277,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 269,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: [\"\\u7528\\u4E8EAI\\u667A\\u80FD\\u5206\\u6790\\u548C\\u5E02\\u573A\\u9884\\u6D4B\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 285,\n                  columnNumber: 19\n                }, this), \"\\u83B7\\u53D6\\u65B9\\u5F0F: \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://platform.deepseek.com/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"platform.deepseek.com\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 286,\n                  columnNumber: 28\n                }, this), \" \\u83B7\\u53D6API Key\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 283,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 251,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 250,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 206,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 292,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u529F\\u80FD\\u8BBE\\u7F6E\",\n          style: {\n            marginTop: '24px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [24, 16],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: ['enabled_features', 'ai_analysis'],\n                label: \"AI\\u667A\\u80FD\\u5206\\u6790\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 303,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 298,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u542F\\u7528AI\\u5E02\\u573A\\u5206\\u6790\\u548C\\u9884\\u6D4B\\u529F\\u80FD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 305,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 297,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: ['enabled_features', 'realtime_data'],\n                label: \"\\u5B9E\\u65F6\\u6570\\u636E\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 314,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 309,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u542F\\u7528\\u5B9E\\u65F6\\u884C\\u60C5\\u6570\\u636E\\u63A8\\u9001\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 316,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 308,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: ['enabled_features', 'notifications'],\n                label: \"\\u6D88\\u606F\\u901A\\u77E5\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 325,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 320,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u542F\\u7528\\u7CFB\\u7EDF\\u6D88\\u606F\\u548C\\u544A\\u8B66\\u901A\\u77E5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 327,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 319,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 296,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 295,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          style: {\n            marginTop: '24px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            size: \"large\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 337,\n                columnNumber: 23\n              }, this),\n              htmlType: \"submit\",\n              loading: loading,\n              size: \"large\",\n              children: \"\\u4FDD\\u5B58\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 335,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 346,\n                columnNumber: 23\n              }, this),\n              onClick: loadConfiguration,\n              size: \"large\",\n              children: \"\\u91CD\\u65B0\\u52A0\\u8F7D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 345,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              danger: true,\n              onClick: resetConfiguration,\n              size: \"large\",\n              children: \"\\u91CD\\u7F6E\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 353,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 334,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 333,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 200,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 199,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 186,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"314DCnZUKJgWgByw9m6YC/L8XFo=\", false, function () {\n  return [Form.useForm];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Form", "Input", "<PERSON><PERSON>", "Space", "Typography", "<PERSON><PERSON>", "Divider", "Switch", "message", "Modal", "Tag", "Row", "Col", "Spin", "SettingOutlined", "KeyOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "ReloadOutlined", "SaveOutlined", "EyeInvisibleOutlined", "EyeTwoTone", "axios", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "confirm", "Settings", "_s", "form", "useForm", "loading", "setLoading", "config", "setConfig", "tushare_token", "deepseek_api_key", "enabled_features", "ai_analysis", "realtime_data", "notifications", "tokenStatus", "setTokenStatus", "tushare", "deepseek", "loadConfiguration", "response", "get", "data", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prev", "error", "console", "testToken", "tokenType", "values", "getFieldsValue", "token", "warning", "post", "token_type", "valid", "success", "saveConfiguration", "validateFields", "resetConfiguration", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "okText", "cancelText", "onOk", "resetFields", "getStatusTag", "status", "color", "children", "size", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "level", "description", "type", "showIcon", "marginBottom", "spinning", "layout", "initialValues", "onFinish", "gutter", "xs", "lg", "extra", "onClick", "<PERSON><PERSON>", "name", "label", "rules", "required", "min", "Password", "placeholder", "iconRender", "visible", "fontSize", "href", "target", "rel", "pattern", "marginTop", "sm", "valuePropName", "textAlign", "htmlType", "danger", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/pages/Settings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  Space,\n  Typography,\n  Alert,\n  Divider,\n  Switch,\n  message,\n  Modal,\n  Tag,\n  Row,\n  Col,\n  Spin\n} from 'antd';\nimport {\n  SettingOutlined,\n  KeyOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ReloadOutlined,\n  SaveOutlined,\n  EyeInvisibleOutlined,\n  EyeTwoTone\n} from '@ant-design/icons';\nimport axios from 'axios';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { confirm } = Modal;\n\ninterface TokenConfig {\n  tushare_token: string;\n  deepseek_api_key: string;\n  enabled_features: {\n    ai_analysis: boolean;\n    realtime_data: boolean;\n    notifications: boolean;\n  };\n}\n\ninterface TokenStatus {\n  tushare: 'valid' | 'invalid' | 'unconfigured' | 'testing';\n  deepseek: 'valid' | 'invalid' | 'unconfigured' | 'testing';\n}\n\nconst Settings: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [config, setConfig] = useState<TokenConfig>({\n    tushare_token: '',\n    deepseek_api_key: '',\n    enabled_features: {\n      ai_analysis: true,\n      realtime_data: true,\n      notifications: true\n    }\n  });\n  const [tokenStatus, setTokenStatus] = useState<TokenStatus>({\n    tushare: 'unconfigured',\n    deepseek: 'unconfigured'\n  });\n\n  useEffect(() => {\n    loadConfiguration();\n  }, []);\n\n  const loadConfiguration = async () => {\n    try {\n      setLoading(true);\n      const response = await axios.get('/api/v1/config/tokens');\n      if (response.data) {\n        setConfig(response.data);\n        form.setFieldsValue(response.data);\n        \n        // 检查token状态\n        if (response.data.tushare_token) {\n          setTokenStatus(prev => ({ ...prev, tushare: 'valid' }));\n        }\n        if (response.data.deepseek_api_key) {\n          setTokenStatus(prev => ({ ...prev, deepseek: 'valid' }));\n        }\n      }\n    } catch (error) {\n      console.error('加载配置失败:', error);\n      message.error('加载配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const testToken = async (tokenType: 'tushare' | 'deepseek') => {\n    const values = form.getFieldsValue();\n    const token = tokenType === 'tushare' ? values.tushare_token : values.deepseek_api_key;\n    \n    if (!token) {\n      message.warning(`请先输入${tokenType === 'tushare' ? 'Tushare' : 'DeepSeek'} Token`);\n      return;\n    }\n\n    try {\n      setTokenStatus(prev => ({ ...prev, [tokenType]: 'testing' }));\n      \n      const response = await axios.post(`/api/v1/config/test-token`, {\n        token_type: tokenType,\n        token: token\n      });\n\n      if (response.data.valid) {\n        setTokenStatus(prev => ({ ...prev, [tokenType]: 'valid' }));\n        message.success(`${tokenType === 'tushare' ? 'Tushare' : 'DeepSeek'} Token验证成功`);\n      } else {\n        setTokenStatus(prev => ({ ...prev, [tokenType]: 'invalid' }));\n        message.error(`Token验证失败: ${response.data.error || '未知错误'}`);\n      }\n    } catch (error) {\n      setTokenStatus(prev => ({ ...prev, [tokenType]: 'invalid' }));\n      message.error('Token验证失败');\n      console.error('Token验证失败:', error);\n    }\n  };\n\n  const saveConfiguration = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      const response = await axios.post('/api/v1/config/tokens', values);\n      \n      if (response.data.success) {\n        setConfig(values);\n        message.success('配置保存成功');\n        \n        // 重新检查token状态\n        if (values.tushare_token) {\n          testToken('tushare');\n        }\n        if (values.deepseek_api_key) {\n          testToken('deepseek');\n        }\n      } else {\n        message.error('配置保存失败');\n      }\n    } catch (error) {\n      console.error('保存配置失败:', error);\n      message.error('保存配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetConfiguration = () => {\n    confirm({\n      title: '确认重置配置',\n      icon: <ExclamationCircleOutlined />,\n      content: '这将清除所有已保存的Token配置，确定要继续吗？',\n      okText: '确定',\n      cancelText: '取消',\n      onOk() {\n        form.resetFields();\n        setTokenStatus({\n          tushare: 'unconfigured',\n          deepseek: 'unconfigured'\n        });\n        message.success('配置已重置');\n      },\n    });\n  };\n\n  const getStatusTag = (status: string) => {\n    switch (status) {\n      case 'valid':\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已验证</Tag>;\n      case 'invalid':\n        return <Tag color=\"error\" icon={<ExclamationCircleOutlined />}>验证失败</Tag>;\n      case 'testing':\n        return <Tag color=\"processing\" icon={<Spin size=\"small\" />}>验证中...</Tag>;\n      default:\n        return <Tag color=\"default\">未配置</Tag>;\n    }\n  };\n\n  return (\n    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n      <Title level={2}>\n        <SettingOutlined /> 系统设置\n      </Title>\n      \n      <Alert\n        message=\"Token配置说明\"\n        description=\"请配置相应的API Token以启用完整功能。所有Token都会安全存储在本地。\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: '24px' }}\n      />\n\n      <Spin spinning={loading}>\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={config}\n          onFinish={saveConfiguration}\n        >\n          <Row gutter={[24, 24]}>\n            {/* Tushare配置 */}\n            <Col xs={24} lg={12}>\n              <Card\n                title={\n                  <Space>\n                    <KeyOutlined />\n                    Tushare Pro配置\n                    {getStatusTag(tokenStatus.tushare)}\n                  </Space>\n                }\n                extra={\n                  <Button\n                    size=\"small\"\n                    onClick={() => testToken('tushare')}\n                    loading={tokenStatus.tushare === 'testing'}\n                  >\n                    测试连接\n                  </Button>\n                }\n              >\n                <Form.Item\n                  name=\"tushare_token\"\n                  label=\"Tushare Token\"\n                  rules={[\n                    { required: true, message: '请输入Tushare Token' },\n                    { min: 32, message: 'Token长度至少32位' }\n                  ]}\n                >\n                  <Input.Password\n                    placeholder=\"请输入Tushare Pro Token\"\n                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                  />\n                </Form.Item>\n                \n                <Paragraph type=\"secondary\" style={{ fontSize: '12px' }}>\n                  用于获取股票数据和行情信息。\n                  <br />\n                  获取方式: 访问 <a href=\"https://tushare.pro/\" target=\"_blank\" rel=\"noopener noreferrer\">tushare.pro</a> 注册并获取Token\n                </Paragraph>\n              </Card>\n            </Col>\n\n            {/* DeepSeek配置 */}\n            <Col xs={24} lg={12}>\n              <Card\n                title={\n                  <Space>\n                    <KeyOutlined />\n                    DeepSeek AI配置\n                    {getStatusTag(tokenStatus.deepseek)}\n                  </Space>\n                }\n                extra={\n                  <Button\n                    size=\"small\"\n                    onClick={() => testToken('deepseek')}\n                    loading={tokenStatus.deepseek === 'testing'}\n                  >\n                    测试连接\n                  </Button>\n                }\n              >\n                <Form.Item\n                  name=\"deepseek_api_key\"\n                  label=\"DeepSeek API Key\"\n                  rules={[\n                    { required: true, message: '请输入DeepSeek API Key' },\n                    { pattern: /^sk-/, message: 'API Key应以sk-开头' }\n                  ]}\n                >\n                  <Input.Password\n                    placeholder=\"请输入DeepSeek API Key\"\n                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                  />\n                </Form.Item>\n                \n                <Paragraph type=\"secondary\" style={{ fontSize: '12px' }}>\n                  用于AI智能分析和市场预测。\n                  <br />\n                  获取方式: 访问 <a href=\"https://platform.deepseek.com/\" target=\"_blank\" rel=\"noopener noreferrer\">platform.deepseek.com</a> 获取API Key\n                </Paragraph>\n              </Card>\n            </Col>\n          </Row>\n\n          <Divider />\n\n          {/* 功能开关 */}\n          <Card title=\"功能设置\" style={{ marginTop: '24px' }}>\n            <Row gutter={[24, 16]}>\n              <Col xs={24} sm={8}>\n                <Form.Item\n                  name={['enabled_features', 'ai_analysis']}\n                  label=\"AI智能分析\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n                <Text type=\"secondary\">启用AI市场分析和预测功能</Text>\n              </Col>\n              \n              <Col xs={24} sm={8}>\n                <Form.Item\n                  name={['enabled_features', 'realtime_data']}\n                  label=\"实时数据\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n                <Text type=\"secondary\">启用实时行情数据推送</Text>\n              </Col>\n              \n              <Col xs={24} sm={8}>\n                <Form.Item\n                  name={['enabled_features', 'notifications']}\n                  label=\"消息通知\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n                <Text type=\"secondary\">启用系统消息和告警通知</Text>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 操作按钮 */}\n          <Card style={{ marginTop: '24px', textAlign: 'center' }}>\n            <Space size=\"large\">\n              <Button\n                type=\"primary\"\n                icon={<SaveOutlined />}\n                htmlType=\"submit\"\n                loading={loading}\n                size=\"large\"\n              >\n                保存配置\n              </Button>\n              \n              <Button\n                icon={<ReloadOutlined />}\n                onClick={loadConfiguration}\n                size=\"large\"\n              >\n                重新加载\n              </Button>\n              \n              <Button\n                danger\n                onClick={resetConfiguration}\n                size=\"large\"\n              >\n                重置配置\n              </Button>\n            </Space>\n          </Card>\n        </Form>\n      </Spin>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,IAAI,QACC,MAAM;AACb,SACEC,eAAe,EACfC,WAAW,EACXC,mBAAmB,EACnBC,yBAAyB,EACzBC,cAAc,EACdC,YAAY,EACZC,oBAAoB,EACpBC,UAAU,QACL,mBAAmB;AAC1B,OAAOC,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGvB,UAAU;AAC7C,MAAM;EAAEwB;AAAQ,CAAC,GAAGnB,KAAK;AAiBzB,MAAMoB,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,IAAI,CAAC,GAAG/B,IAAI,CAACgC,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsC,MAAM,EAAEC,SAAS,CAAC,GAAGvC,QAAQ,CAAc;IAChDwC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,gBAAgB,EAAE;MAChBC,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG/C,QAAQ,CAAc;IAC1DgD,OAAO,EAAE,cAAc;IACvBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEFhD,SAAS,CAAC,MAAM;IACdiD,iBAAiB,CAAC,CAAC;EACrB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACFb,UAAU,CAAC,IAAI,CAAC;MAChB,MAAMc,QAAQ,GAAG,MAAM1B,KAAK,CAAC2B,GAAG,CAAC,uBAAuB,CAAC;MACzD,IAAID,QAAQ,CAACE,IAAI,EAAE;QACjBd,SAAS,CAACY,QAAQ,CAACE,IAAI,CAAC;QACxBnB,IAAI,CAACoB,cAAc,CAACH,QAAQ,CAACE,IAAI,CAAC;;QAElC;QACA,IAAIF,QAAQ,CAACE,IAAI,CAACb,aAAa,EAAE;UAC/BO,cAAc,CAACQ,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEP,OAAO,EAAE;UAAQ,CAAC,CAAC,CAAC;QACzD;QACA,IAAIG,QAAQ,CAACE,IAAI,CAACZ,gBAAgB,EAAE;UAClCM,cAAc,CAACQ,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEN,QAAQ,EAAE;UAAQ,CAAC,CAAC,CAAC;QAC1D;MACF;IACF,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7C,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMqB,SAAS,GAAG,MAAOC,SAAiC,IAAK;IAC7D,MAAMC,MAAM,GAAG1B,IAAI,CAAC2B,cAAc,CAAC,CAAC;IACpC,MAAMC,KAAK,GAAGH,SAAS,KAAK,SAAS,GAAGC,MAAM,CAACpB,aAAa,GAAGoB,MAAM,CAACnB,gBAAgB;IAEtF,IAAI,CAACqB,KAAK,EAAE;MACVnD,OAAO,CAACoD,OAAO,CAAC,OAAOJ,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,UAAU,QAAQ,CAAC;MAChF;IACF;IAEA,IAAI;MACFZ,cAAc,CAACQ,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACI,SAAS,GAAG;MAAU,CAAC,CAAC,CAAC;MAE7D,MAAMR,QAAQ,GAAG,MAAM1B,KAAK,CAACuC,IAAI,CAAC,2BAA2B,EAAE;QAC7DC,UAAU,EAAEN,SAAS;QACrBG,KAAK,EAAEA;MACT,CAAC,CAAC;MAEF,IAAIX,QAAQ,CAACE,IAAI,CAACa,KAAK,EAAE;QACvBnB,cAAc,CAACQ,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACI,SAAS,GAAG;QAAQ,CAAC,CAAC,CAAC;QAC3DhD,OAAO,CAACwD,OAAO,CAAC,GAAGR,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,UAAU,YAAY,CAAC;MAClF,CAAC,MAAM;QACLZ,cAAc,CAACQ,IAAI,KAAK;UAAE,GAAGA,IAAI;UAAE,CAACI,SAAS,GAAG;QAAU,CAAC,CAAC,CAAC;QAC7DhD,OAAO,CAAC6C,KAAK,CAAC,cAAcL,QAAQ,CAACE,IAAI,CAACG,KAAK,IAAI,MAAM,EAAE,CAAC;MAC9D;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdT,cAAc,CAACQ,IAAI,KAAK;QAAE,GAAGA,IAAI;QAAE,CAACI,SAAS,GAAG;MAAU,CAAC,CAAC,CAAC;MAC7DhD,OAAO,CAAC6C,KAAK,CAAC,WAAW,CAAC;MAC1BC,OAAO,CAACD,KAAK,CAAC,YAAY,EAAEA,KAAK,CAAC;IACpC;EACF,CAAC;EAED,MAAMY,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMR,MAAM,GAAG,MAAM1B,IAAI,CAACmC,cAAc,CAAC,CAAC;MAC1ChC,UAAU,CAAC,IAAI,CAAC;MAEhB,MAAMc,QAAQ,GAAG,MAAM1B,KAAK,CAACuC,IAAI,CAAC,uBAAuB,EAAEJ,MAAM,CAAC;MAElE,IAAIT,QAAQ,CAACE,IAAI,CAACc,OAAO,EAAE;QACzB5B,SAAS,CAACqB,MAAM,CAAC;QACjBjD,OAAO,CAACwD,OAAO,CAAC,QAAQ,CAAC;;QAEzB;QACA,IAAIP,MAAM,CAACpB,aAAa,EAAE;UACxBkB,SAAS,CAAC,SAAS,CAAC;QACtB;QACA,IAAIE,MAAM,CAACnB,gBAAgB,EAAE;UAC3BiB,SAAS,CAAC,UAAU,CAAC;QACvB;MACF,CAAC,MAAM;QACL/C,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;MACzB;IACF,CAAC,CAAC,OAAOA,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B7C,OAAO,CAAC6C,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRnB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMiC,kBAAkB,GAAGA,CAAA,KAAM;IAC/BvC,OAAO,CAAC;MACNwC,KAAK,EAAE,QAAQ;MACfC,IAAI,eAAE7C,OAAA,CAACP,yBAAyB;QAAAqD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,EAAE,2BAA2B;MACpCC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAIA,CAAA,EAAG;QACL9C,IAAI,CAAC+C,WAAW,CAAC,CAAC;QAClBlC,cAAc,CAAC;UACbC,OAAO,EAAE,cAAc;UACvBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFtC,OAAO,CAACwD,OAAO,CAAC,OAAO,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMe,YAAY,GAAIC,MAAc,IAAK;IACvC,QAAQA,MAAM;MACZ,KAAK,OAAO;QACV,oBAAOxD,OAAA,CAACd,GAAG;UAACuE,KAAK,EAAC,SAAS;UAACZ,IAAI,eAAE7C,OAAA,CAACR,mBAAmB;YAAAsD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAS,QAAA,EAAC;QAAG;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACtE,KAAK,SAAS;QACZ,oBAAOjD,OAAA,CAACd,GAAG;UAACuE,KAAK,EAAC,OAAO;UAACZ,IAAI,eAAE7C,OAAA,CAACP,yBAAyB;YAAAqD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAS,QAAA,EAAC;QAAI;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC3E,KAAK,SAAS;QACZ,oBAAOjD,OAAA,CAACd,GAAG;UAACuE,KAAK,EAAC,YAAY;UAACZ,IAAI,eAAE7C,OAAA,CAACX,IAAI;YAACsE,IAAI,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAS,QAAA,EAAC;QAAM;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC1E;QACE,oBAAOjD,OAAA,CAACd,GAAG;UAACuE,KAAK,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAG;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACzC;EACF,CAAC;EAED,oBACEjD,OAAA;IAAK4D,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAL,QAAA,gBACpE1D,OAAA,CAACC,KAAK;MAAC+D,KAAK,EAAE,CAAE;MAAAN,QAAA,gBACd1D,OAAA,CAACV,eAAe;QAAAwD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BACrB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERjD,OAAA,CAACnB,KAAK;MACJG,OAAO,EAAC,+BAAW;MACnBiF,WAAW,EAAC,4KAA0C;MACtDC,IAAI,EAAC,MAAM;MACXC,QAAQ;MACRP,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAO;IAAE;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFjD,OAAA,CAACX,IAAI;MAACgF,QAAQ,EAAE5D,OAAQ;MAAAiD,QAAA,eACtB1D,OAAA,CAACxB,IAAI;QACH+B,IAAI,EAAEA,IAAK;QACX+D,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAE5D,MAAO;QACtB6D,QAAQ,EAAE/B,iBAAkB;QAAAiB,QAAA,gBAE5B1D,OAAA,CAACb,GAAG;UAACsF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAf,QAAA,gBAEpB1D,OAAA,CAACZ,GAAG;YAACsF,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAjB,QAAA,eAClB1D,OAAA,CAACzB,IAAI;cACHqE,KAAK,eACH5C,OAAA,CAACrB,KAAK;gBAAA+E,QAAA,gBACJ1D,OAAA,CAACT,WAAW;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,2BAEf,EAACM,YAAY,CAACpC,WAAW,CAACE,OAAO,CAAC;cAAA;gBAAAyB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CACR;cACD2B,KAAK,eACH5E,OAAA,CAACtB,MAAM;gBACLiF,IAAI,EAAC,OAAO;gBACZkB,OAAO,EAAEA,CAAA,KAAM9C,SAAS,CAAC,SAAS,CAAE;gBACpCtB,OAAO,EAAEU,WAAW,CAACE,OAAO,KAAK,SAAU;gBAAAqC,QAAA,EAC5C;cAED;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;cAAAS,QAAA,gBAED1D,OAAA,CAACxB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,eAAe;gBACpBC,KAAK,EAAC,eAAe;gBACrBC,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAElG,OAAO,EAAE;gBAAmB,CAAC,EAC/C;kBAAEmG,GAAG,EAAE,EAAE;kBAAEnG,OAAO,EAAE;gBAAe,CAAC,CACpC;gBAAA0E,QAAA,eAEF1D,OAAA,CAACvB,KAAK,CAAC2G,QAAQ;kBACbC,WAAW,EAAC,qCAAsB;kBAClCC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGvF,OAAA,CAACH,UAAU;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjD,OAAA,CAACJ,oBAAoB;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAG;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZjD,OAAA,CAACG,SAAS;gBAAC+D,IAAI,EAAC,WAAW;gBAACN,KAAK,EAAE;kBAAE4B,QAAQ,EAAE;gBAAO,CAAE;gBAAA9B,QAAA,GAAC,sFAEvD,eAAA1D,OAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,2CACG,eAAAjD,OAAA;kBAAGyF,IAAI,EAAC,sBAAsB;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAAjC,QAAA,EAAC;gBAAW;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wCACnG;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNjD,OAAA,CAACZ,GAAG;YAACsF,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAjB,QAAA,eAClB1D,OAAA,CAACzB,IAAI;cACHqE,KAAK,eACH5C,OAAA,CAACrB,KAAK;gBAAA+E,QAAA,gBACJ1D,OAAA,CAACT,WAAW;kBAAAuD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,2BAEf,EAACM,YAAY,CAACpC,WAAW,CAACG,QAAQ,CAAC;cAAA;gBAAAwB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CACR;cACD2B,KAAK,eACH5E,OAAA,CAACtB,MAAM;gBACLiF,IAAI,EAAC,OAAO;gBACZkB,OAAO,EAAEA,CAAA,KAAM9C,SAAS,CAAC,UAAU,CAAE;gBACrCtB,OAAO,EAAEU,WAAW,CAACG,QAAQ,KAAK,SAAU;gBAAAoC,QAAA,EAC7C;cAED;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;cAAAS,QAAA,gBAED1D,OAAA,CAACxB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBC,KAAK,EAAC,kBAAkB;gBACxBC,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAElG,OAAO,EAAE;gBAAsB,CAAC,EAClD;kBAAE4G,OAAO,EAAE,MAAM;kBAAE5G,OAAO,EAAE;gBAAiB,CAAC,CAC9C;gBAAA0E,QAAA,eAEF1D,OAAA,CAACvB,KAAK,CAAC2G,QAAQ;kBACbC,WAAW,EAAC,oCAAqB;kBACjCC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAGvF,OAAA,CAACH,UAAU;oBAAAiD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGjD,OAAA,CAACJ,oBAAoB;oBAAAkD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAG;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZjD,OAAA,CAACG,SAAS;gBAAC+D,IAAI,EAAC,WAAW;gBAACN,KAAK,EAAE;kBAAE4B,QAAQ,EAAE;gBAAO,CAAE;gBAAA9B,QAAA,GAAC,4EAEvD,eAAA1D,OAAA;kBAAA8C,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,2CACG,eAAAjD,OAAA;kBAAGyF,IAAI,EAAC,gCAAgC;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAAjC,QAAA,EAAC;gBAAqB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wBACvH;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENjD,OAAA,CAAClB,OAAO;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGXjD,OAAA,CAACzB,IAAI;UAACqE,KAAK,EAAC,0BAAM;UAACgB,KAAK,EAAE;YAAEiC,SAAS,EAAE;UAAO,CAAE;UAAAnC,QAAA,eAC9C1D,OAAA,CAACb,GAAG;YAACsF,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAAf,QAAA,gBACpB1D,OAAA,CAACZ,GAAG;cAACsF,EAAE,EAAE,EAAG;cAACoB,EAAE,EAAE,CAAE;cAAApC,QAAA,gBACjB1D,OAAA,CAACxB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAE;gBAC1CC,KAAK,EAAC,4BAAQ;gBACde,aAAa,EAAC,SAAS;gBAAArC,QAAA,eAEvB1D,OAAA,CAACjB,MAAM;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACZjD,OAAA,CAACE,IAAI;gBAACgE,IAAI,EAAC,WAAW;gBAAAR,QAAA,EAAC;cAAa;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eAENjD,OAAA,CAACZ,GAAG;cAACsF,EAAE,EAAE,EAAG;cAACoB,EAAE,EAAE,CAAE;cAAApC,QAAA,gBACjB1D,OAAA,CAACxB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAE,CAAC,kBAAkB,EAAE,eAAe,CAAE;gBAC5CC,KAAK,EAAC,0BAAM;gBACZe,aAAa,EAAC,SAAS;gBAAArC,QAAA,eAEvB1D,OAAA,CAACjB,MAAM;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACZjD,OAAA,CAACE,IAAI;gBAACgE,IAAI,EAAC,WAAW;gBAAAR,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eAENjD,OAAA,CAACZ,GAAG;cAACsF,EAAE,EAAE,EAAG;cAACoB,EAAE,EAAE,CAAE;cAAApC,QAAA,gBACjB1D,OAAA,CAACxB,IAAI,CAACsG,IAAI;gBACRC,IAAI,EAAE,CAAC,kBAAkB,EAAE,eAAe,CAAE;gBAC5CC,KAAK,EAAC,0BAAM;gBACZe,aAAa,EAAC,SAAS;gBAAArC,QAAA,eAEvB1D,OAAA,CAACjB,MAAM;kBAAA+D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACZjD,OAAA,CAACE,IAAI;gBAACgE,IAAI,EAAC,WAAW;gBAAAR,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPjD,OAAA,CAACzB,IAAI;UAACqF,KAAK,EAAE;YAAEiC,SAAS,EAAE,MAAM;YAAEG,SAAS,EAAE;UAAS,CAAE;UAAAtC,QAAA,eACtD1D,OAAA,CAACrB,KAAK;YAACgF,IAAI,EAAC,OAAO;YAAAD,QAAA,gBACjB1D,OAAA,CAACtB,MAAM;cACLwF,IAAI,EAAC,SAAS;cACdrB,IAAI,eAAE7C,OAAA,CAACL,YAAY;gBAAAmD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBgD,QAAQ,EAAC,QAAQ;cACjBxF,OAAO,EAAEA,OAAQ;cACjBkD,IAAI,EAAC,OAAO;cAAAD,QAAA,EACb;YAED;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETjD,OAAA,CAACtB,MAAM;cACLmE,IAAI,eAAE7C,OAAA,CAACN,cAAc;gBAAAoD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzB4B,OAAO,EAAEtD,iBAAkB;cAC3BoC,IAAI,EAAC,OAAO;cAAAD,QAAA,EACb;YAED;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETjD,OAAA,CAACtB,MAAM;cACLwH,MAAM;cACNrB,OAAO,EAAElC,kBAAmB;cAC5BgB,IAAI,EAAC,OAAO;cAAAD,QAAA,EACb;YAED;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAAC3C,EAAA,CA7TID,QAAkB;EAAA,QACP7B,IAAI,CAACgC,OAAO;AAAA;AAAA2F,EAAA,GADvB9F,QAAkB;AA+TxB,eAAeA,QAAQ;AAAC,IAAA8F,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}