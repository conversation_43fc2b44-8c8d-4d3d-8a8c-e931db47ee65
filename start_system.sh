#!/bin/bash

# 量化交易监控系统启动脚本 (Linux/Mac)
# Quantitative Trading System Startup Script

echo "========================================"
echo "🚀 量化交易监控系统启动脚本"
echo "========================================"
echo

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查Python是否安装
echo -e "${BLUE}[1/6] 检查Python环境...${NC}"
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3未安装或未添加到PATH${NC}"
    echo "请安装Python 3.8+并添加到系统PATH"
    exit 1
fi
echo -e "${GREEN}✅ Python环境检查通过${NC}"

# 检查Node.js是否安装
echo -e "${BLUE}[2/6] 检查Node.js环境...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js未安装或未添加到PATH${NC}"
    echo "请安装Node.js 16+并添加到系统PATH"
    exit 1
fi
echo -e "${GREEN}✅ Node.js环境检查通过${NC}"

# 安装后端依赖
echo -e "${BLUE}[3/6] 安装后端依赖...${NC}"
cd backend

if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt

if [ $? -ne 0 ]; then
    echo -e "${RED}❌ 后端依赖安装失败${NC}"
    exit 1
fi

echo -e "${GREEN}✅ 后端依赖安装完成${NC}"
cd ..

# 安装前端依赖
echo -e "${BLUE}[4/6] 安装前端依赖...${NC}"
cd frontend

if [ ! -d "node_modules" ]; then
    echo "安装前端依赖包..."
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 前端依赖安装失败${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ 前端依赖安装完成${NC}"
cd ..

# 创建必要目录
echo -e "${BLUE}[5/6] 创建必要目录...${NC}"
mkdir -p backend/logs
mkdir -p data
echo -e "${GREEN}✅ 目录创建完成${NC}"

# 启动系统
echo -e "${BLUE}[6/6] 启动系统服务...${NC}"
echo
echo -e "${YELLOW}🔥 正在启动量化交易监控系统...${NC}"
echo
echo -e "${GREEN}📊 后端API: http://localhost:8000${NC}"
echo -e "${GREEN}🌐 前端界面: http://localhost:3001${NC}"
echo -e "${GREEN}📡 WebSocket: ws://localhost:8000/ws${NC}"
echo

# 启动后端服务
echo "启动后端服务..."
cd backend
source venv/bin/activate
nohup python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload > logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "后端服务PID: $BACKEND_PID"
cd ..

# 等待后端启动
echo "等待后端服务启动..."
sleep 5

# 启动前端服务
echo "启动前端服务..."
cd frontend
PORT=3001 nohup npm start > ../backend/logs/frontend.log 2>&1 &
FRONTEND_PID=$!
echo "前端服务PID: $FRONTEND_PID"
cd ..

# 保存PID到文件
echo $BACKEND_PID > backend/logs/backend.pid
echo $FRONTEND_PID > backend/logs/frontend.pid

echo
echo "========================================"
echo -e "${GREEN}✅ 系统启动完成！${NC}"
echo "========================================"
echo
echo -e "${BLUE}📝 使用说明:${NC}"
echo "1. 后端API服务: http://localhost:8000"
echo "2. 前端Web界面: http://localhost:3001"
echo "3. API文档: http://localhost:8000/docs"
echo "4. 系统健康检查: http://localhost:8000/health"
echo
echo -e "${YELLOW}🔧 停止服务命令:${NC}"
echo "./stop_system.sh"
echo
echo -e "${BLUE}📚 更多信息请查看 README.md 文件${NC}"
echo
