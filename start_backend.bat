@echo off
echo ========================================
echo Starting Backend Service
echo ========================================
echo.

echo Current directory: %CD%
echo.

echo Checking if backend directory exists...
if not exist "backend" (
    echo ERROR: backend directory not found
    echo Please run this script from the project root directory
    pause
    exit /b 1
)

echo Changing to backend directory...
cd backend
echo Backend directory: %CD%

echo.
echo Checking Python...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    pause
    exit /b 1
)

echo.
echo Installing/checking dependencies...
pip install fastapi uvicorn pandas numpy tushare requests

echo.
echo Starting FastAPI server...
echo Backend will be available at: http://localhost:8000
echo API documentation at: http://localhost:8000/docs
echo.
echo Press Ctrl+C to stop the server
echo.

python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

pause
