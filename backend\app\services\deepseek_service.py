"""
DeepSeek服务包装器
"""
import logging
from typing import Optional, Dict, Any
from ai.deepseek_client import DeepSeekClient

logger = logging.getLogger(__name__)

class DeepSeekService:
    """DeepSeek服务包装器"""
    
    def __init__(self, api_key: str = None):
        """
        初始化DeepSeek服务
        
        Args:
            api_key: DeepSeek API密钥
        """
        self.api_key = api_key
        self._client = None
    
    @property
    def client(self) -> Optional[DeepSeekClient]:
        """获取DeepSeek客户端实例"""
        if self._client is None and self.api_key:
            try:
                self._client = DeepSeekClient(api_key=self.api_key)
            except Exception as e:
                logger.error(f"初始化DeepSeek客户端失败: {e}")
                return None
        return self._client
    
    def test_connection(self) -> Dict[str, Any]:
        """
        测试API连接
        
        Returns:
            Dict: 包含连接状态的字典
        """
        try:
            if not self.client:
                return {
                    "success": False,
                    "error": "无法初始化DeepSeek客户端"
                }
            
            # 发送简单的测试请求
            test_prompt = "请回复'连接成功'来确认API连接正常。"
            response = self.client.analyze_market_sentiment(test_prompt)
            
            if response:
                return {
                    "success": True,
                    "message": "DeepSeek API连接成功",
                    "response": response[:100] if len(response) > 100 else response
                }
            else:
                return {
                    "success": False,
                    "error": "API响应为空"
                }
                
        except Exception as e:
            logger.error(f"测试DeepSeek连接失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def analyze_market_sentiment(self, text: str) -> Optional[str]:
        """
        分析市场情绪
        
        Args:
            text: 要分析的文本
            
        Returns:
            str: 分析结果
        """
        try:
            if not self.client:
                return None
            
            return self.client.analyze_market_sentiment(text)
        except Exception as e:
            logger.error(f"市场情绪分析失败: {e}")
            return None
    
    def identify_risk_events(self, text: str) -> Optional[str]:
        """
        识别风险事件
        
        Args:
            text: 要分析的文本
            
        Returns:
            str: 风险分析结果
        """
        try:
            if not self.client:
                return None
            
            return self.client.identify_risk_events(text)
        except Exception as e:
            logger.error(f"风险事件识别失败: {e}")
            return None
    
    def generate_investment_advice(self, market_data: Dict[str, Any]) -> Optional[str]:
        """
        生成投资建议
        
        Args:
            market_data: 市场数据
            
        Returns:
            str: 投资建议
        """
        try:
            if not self.client:
                return None
            
            return self.client.generate_investment_advice(market_data)
        except Exception as e:
            logger.error(f"生成投资建议失败: {e}")
            return None
    
    def get_api_status(self) -> Dict[str, Any]:
        """
        获取API状态信息
        
        Returns:
            Dict: API状态信息
        """
        if not self.client:
            return {
                "connected": False,
                "api_key_configured": bool(self.api_key),
                "call_count": 0,
                "total_tokens": 0
            }
        
        return {
            "connected": True,
            "api_key_configured": bool(self.api_key),
            "call_count": getattr(self.client, 'call_count', 0),
            "total_tokens": getattr(self.client, 'total_tokens', 0)
        }
