"""
数据源模块初始化
"""
from .base_data_source import BaseDataSource, DataSourceManager, data_source_manager
from .wind_client import WindDataSource, get_wind_client
from .ricequant_client import RiceQuantDataSource, get_ricequant_client

# 延迟导入Tushare客户端以避免循环导入
# from ..tushare_client import TushareClient, get_tushare_client

import logging
from app.core.config import settings

logger = logging.getLogger(__name__)

def initialize_data_sources():
    """
    初始化所有数据源
    """
    logger.info("🔄 初始化数据源...")

    # 1. 注册Tushare数据源（主数据源）
    try:
        # 延迟导入以避免循环导入
        from ..tushare_client import get_tushare_client
        tushare_client = get_tushare_client()
        # 注意：TushareClient需要适配BaseDataSource接口
        logger.info("✅ Tushare数据源已注册（主数据源）")
    except Exception as e:
        logger.error(f"❌ Tushare数据源注册失败: {e}")
    
    # 2. 注册万得数据源（如果启用）
    if settings.WIND_ENABLED:
        try:
            wind_client = get_wind_client()
            data_source_manager.register_data_source(wind_client)
            logger.info("✅ 万得数据源已注册")
        except Exception as e:
            logger.error(f"❌ 万得数据源注册失败: {e}")
    else:
        logger.info("⚠️ 万得数据源未启用")
    
    # 3. 注册米筐数据源（如果启用）
    if settings.RICEQUANT_ENABLED:
        try:
            ricequant_client = get_ricequant_client()
            data_source_manager.register_data_source(ricequant_client)
            logger.info("✅ 米筐数据源已注册")
        except Exception as e:
            logger.error(f"❌ 米筐数据源注册失败: {e}")
    else:
        logger.info("⚠️ 米筐数据源未启用")
    
    # 4. 连接所有数据源
    connection_results = data_source_manager.connect_all()
    
    # 5. 输出连接状态
    logger.info("📊 数据源连接状态:")
    for source_name, connected in connection_results.items():
        status = "✅ 已连接" if connected else "❌ 连接失败"
        logger.info(f"  {source_name}: {status}")
    
    # 6. 返回状态信息
    return data_source_manager.get_status()

def get_available_data_sources():
    """
    获取可用的数据源列表
    
    Returns:
        List[str]: 可用数据源名称列表
    """
    return data_source_manager.get_available_sources()

def get_primary_data_source():
    """
    获取主数据源
    
    Returns:
        BaseDataSource: 主数据源实例
    """
    return data_source_manager.get_data_source()

def get_data_source_by_name(name: str):
    """
    根据名称获取数据源
    
    Args:
        name: 数据源名称
        
    Returns:
        BaseDataSource: 数据源实例
    """
    return data_source_manager.get_data_source(name)

def get_data_sources_status():
    """
    获取所有数据源状态
    
    Returns:
        Dict: 数据源状态信息
    """
    return data_source_manager.get_status()

# 数据源配置信息
DATA_SOURCE_INFO = {
    "tushare": {
        "name": "Tushare Pro",
        "description": "主要数据源，支持A股+港股",
        "markets": ["A股", "港股"],
        "features": ["实时数据", "历史数据", "基本面数据"],
        "cost": "免费/付费",
        "status": "已集成"
    },
    "wind": {
        "name": "万得(Wind)",
        "description": "机构级数据质量，成本较高",
        "markets": ["A股", "港股", "美股", "期货", "债券"],
        "features": ["实时数据", "历史数据", "基本面数据", "宏观数据"],
        "cost": "年费数万元",
        "status": "预留接口"
    },
    "ricequant": {
        "name": "米筐(RiceQuant)",
        "description": "性价比较好，API稳定",
        "markets": ["A股", "港股", "美股"],
        "features": ["实时数据", "历史数据", "基本面数据"],
        "cost": "免费额度+付费",
        "status": "预留接口"
    }
}

def get_data_source_info():
    """
    获取数据源信息
    
    Returns:
        Dict: 数据源信息
    """
    return DATA_SOURCE_INFO

__all__ = [
    'BaseDataSource',
    'DataSourceManager', 
    'data_source_manager',
    'WindDataSource',
    'RiceQuantDataSource',
    'initialize_data_sources',
    'get_available_data_sources',
    'get_primary_data_source',
    'get_data_source_by_name',
    'get_data_sources_status',
    'get_data_source_info',
    'DATA_SOURCE_INFO'
]
