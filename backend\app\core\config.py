"""
应用配置设置
"""
from typing import List
import os

class Settings:
    """应用配置类"""

    # 基础配置
    PROJECT_NAME: str = "量化交易监控系统"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"

    # CORS配置
    BACKEND_CORS_ORIGINS: List[str] = ["http://localhost:3001", "http://localhost:3000", "http://localhost:8080"]

    # 数据库配置
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "password"
    POSTGRES_DB: str = "quant_trading"
    POSTGRES_PORT: str = "5432"

    @property
    def SQLALCHEMY_DATABASE_URI(self) -> str:
        return f"postgresql://{self.POSTGRES_USER}:{self.POSTGRES_PASSWORD}@{self.POSTGRES_SERVER}:{self.POSTGRES_PORT}/{self.POSTGRES_DB}"
    
    # InfluxDB配置
    INFLUXDB_URL: str = "http://localhost:8086"
    INFLUXDB_TOKEN: str = ""
    INFLUXDB_ORG: str = "quant-trading"
    INFLUXDB_BUCKET: str = "market_data"
    
    # Redis配置
    REDIS_URL: str = "redis://localhost:6379"
    
    # Tushare配置
    TUSHARE_TOKEN: str = os.getenv("TUSHARE_TOKEN", "772e043e246ef24189447f73f0c276e7fc98f18a0cb7cce72bd0f28d")

    # DeepSeek API配置
    DEEPSEEK_API_KEY: str = os.getenv("DEEPSEEK_API_KEY", "***********************************")
    DEEPSEEK_BASE_URL: str = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com")

    # 万得(Wind) API配置 - 预留接口
    WIND_USERNAME: str = ""  # 万得用户名
    WIND_PASSWORD: str = ""  # 万得密码
    WIND_ENABLED: bool = False  # 是否启用万得数据源

    # 米筐(RiceQuant) API配置 - 预留接口
    RICEQUANT_API_KEY: str = ""  # 米筐API密钥
    RICEQUANT_BASE_URL: str = "https://api.ricequant.com"  # 米筐API地址
    RICEQUANT_ENABLED: bool = False  # 是否启用米筐数据源

    # 数据更新配置
    DATA_UPDATE_INTERVAL: int = 60  # 秒
    MAX_CONCURRENT_STOCKS: int = 200  # 最大同时监控股票数

    # 日志配置
    LOG_LEVEL: str = "INFO"

# 创建配置实例
settings = Settings()
