{"ast": null, "code": "import { useRef } from 'react';\nexport default (function (isScrollAtTop, isScrollAtBottom, isScrollAtLeft, isScrollAtRight) {\n  // Do lock for a wheel when scrolling\n  var lockRef = useRef(false);\n  var lockTimeoutRef = useRef(null);\n  function lockScroll() {\n    clearTimeout(lockTimeoutRef.current);\n    lockRef.current = true;\n    lockTimeoutRef.current = setTimeout(function () {\n      lockRef.current = false;\n    }, 50);\n  }\n\n  // Pass to ref since global add is in closure\n  var scrollPingRef = useRef({\n    top: isScrollAtTop,\n    bottom: isScrollAtBottom,\n    left: isScrollAtLeft,\n    right: isScrollAtRight\n  });\n  scrollPingRef.current.top = isScrollAtTop;\n  scrollPingRef.current.bottom = isScrollAtBottom;\n  scrollPingRef.current.left = isScrollAtLeft;\n  scrollPingRef.current.right = isScrollAtRight;\n  return function (isHorizontal, delta) {\n    var smoothOffset = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : false;\n    var originScroll = isHorizontal ?\n    // Pass origin wheel when on the left\n    delta < 0 && scrollPingRef.current.left ||\n    // Pass origin wheel when on the right\n    delta > 0 && scrollPingRef.current.right // Pass origin wheel when on the top\n    : delta < 0 && scrollPingRef.current.top ||\n    // Pass origin wheel when on the bottom\n    delta > 0 && scrollPingRef.current.bottom;\n    if (smoothOffset && originScroll) {\n      // No need lock anymore when it's smooth offset from touchMove interval\n      clearTimeout(lockTimeoutRef.current);\n      lockRef.current = false;\n    } else if (!originScroll || lockRef.current) {\n      lockScroll();\n    }\n    return !lockRef.current && originScroll;\n  };\n});", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}