{"ast": null, "code": "import { isString } from '../core/util.js';\nexport function parseXML(svg) {\n  if (isString(svg)) {\n    var parser = new DOMParser();\n    svg = parser.parseFromString(svg, 'text/xml');\n  }\n  var svgNode = svg;\n  if (svgNode.nodeType === 9) {\n    svgNode = svgNode.firstChild;\n  }\n  while (svgNode.nodeName.toLowerCase() !== 'svg' || svgNode.nodeType !== 1) {\n    svgNode = svgNode.nextSibling;\n  }\n  return svgNode;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}