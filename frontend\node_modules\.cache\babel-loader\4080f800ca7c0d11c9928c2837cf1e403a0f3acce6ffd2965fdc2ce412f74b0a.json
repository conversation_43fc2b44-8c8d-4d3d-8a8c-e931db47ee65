{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/esm/objectSpread2\";\nimport _defineProperty from \"@babel/runtime/helpers/esm/defineProperty\";\nimport _slicedToArray from \"@babel/runtime/helpers/esm/slicedToArray\";\nimport classNames from 'classnames';\nimport raf from \"rc-util/es/raf\";\nimport * as React from 'react';\nimport { getPageXY } from \"./hooks/useScrollDrag\";\nvar ScrollBar = /*#__PURE__*/React.forwardRef(function (props, ref) {\n  var prefixCls = props.prefixCls,\n    rtl = props.rtl,\n    scrollOffset = props.scrollOffset,\n    scrollRange = props.scrollRange,\n    onStartMove = props.onStartMove,\n    onStopMove = props.onStopMove,\n    onScroll = props.onScroll,\n    horizontal = props.horizontal,\n    spinSize = props.spinSize,\n    containerSize = props.containerSize,\n    style = props.style,\n    propsThumbStyle = props.thumbStyle,\n    showScrollBar = props.showScrollBar;\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    dragging = _React$useState2[0],\n    setDragging = _React$useState2[1];\n  var _React$useState3 = React.useState(null),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    pageXY = _React$useState4[0],\n    setPageXY = _React$useState4[1];\n  var _React$useState5 = React.useState(null),\n    _React$useState6 = _slicedToArray(_React$useState5, 2),\n    startTop = _React$useState6[0],\n    setStartTop = _React$useState6[1];\n  var isLTR = !rtl;\n\n  // ========================= Refs =========================\n  var scrollbarRef = React.useRef();\n  var thumbRef = React.useRef();\n\n  // ======================= Visible ========================\n  var _React$useState7 = React.useState(showScrollBar),\n    _React$useState8 = _slicedToArray(_React$useState7, 2),\n    visible = _React$useState8[0],\n    setVisible = _React$useState8[1];\n  var visibleTimeoutRef = React.useRef();\n  var delayHidden = function delayHidden() {\n    if (showScrollBar === true || showScrollBar === false) return;\n    clearTimeout(visibleTimeoutRef.current);\n    setVisible(true);\n    visibleTimeoutRef.current = setTimeout(function () {\n      setVisible(false);\n    }, 3000);\n  };\n\n  // ======================== Range =========================\n  var enableScrollRange = scrollRange - containerSize || 0;\n  var enableOffsetRange = containerSize - spinSize || 0;\n\n  // ========================= Top ==========================\n  var top = React.useMemo(function () {\n    if (scrollOffset === 0 || enableScrollRange === 0) {\n      return 0;\n    }\n    var ptg = scrollOffset / enableScrollRange;\n    return ptg * enableOffsetRange;\n  }, [scrollOffset, enableScrollRange, enableOffsetRange]);\n\n  // ====================== Container =======================\n  var onContainerMouseDown = function onContainerMouseDown(e) {\n    e.stopPropagation();\n    e.preventDefault();\n  };\n\n  // ======================== Thumb =========================\n  var stateRef = React.useRef({\n    top: top,\n    dragging: dragging,\n    pageY: pageXY,\n    startTop: startTop\n  });\n  stateRef.current = {\n    top: top,\n    dragging: dragging,\n    pageY: pageXY,\n    startTop: startTop\n  };\n  var onThumbMouseDown = function onThumbMouseDown(e) {\n    setDragging(true);\n    setPageXY(getPageXY(e, horizontal));\n    setStartTop(stateRef.current.top);\n    onStartMove();\n    e.stopPropagation();\n    e.preventDefault();\n  };\n\n  // ======================== Effect ========================\n\n  // React make event as passive, but we need to preventDefault\n  // Add event on dom directly instead.\n  // ref: https://github.com/facebook/react/issues/9809\n  React.useEffect(function () {\n    var onScrollbarTouchStart = function onScrollbarTouchStart(e) {\n      e.preventDefault();\n    };\n    var scrollbarEle = scrollbarRef.current;\n    var thumbEle = thumbRef.current;\n    scrollbarEle.addEventListener('touchstart', onScrollbarTouchStart, {\n      passive: false\n    });\n    thumbEle.addEventListener('touchstart', onThumbMouseDown, {\n      passive: false\n    });\n    return function () {\n      scrollbarEle.removeEventListener('touchstart', onScrollbarTouchStart);\n      thumbEle.removeEventListener('touchstart', onThumbMouseDown);\n    };\n  }, []);\n\n  // Pass to effect\n  var enableScrollRangeRef = React.useRef();\n  enableScrollRangeRef.current = enableScrollRange;\n  var enableOffsetRangeRef = React.useRef();\n  enableOffsetRangeRef.current = enableOffsetRange;\n  React.useEffect(function () {\n    if (dragging) {\n      var moveRafId;\n      var onMouseMove = function onMouseMove(e) {\n        var _stateRef$current = stateRef.current,\n          stateDragging = _stateRef$current.dragging,\n          statePageY = _stateRef$current.pageY,\n          stateStartTop = _stateRef$current.startTop;\n        raf.cancel(moveRafId);\n        var rect = scrollbarRef.current.getBoundingClientRect();\n        var scale = containerSize / (horizontal ? rect.width : rect.height);\n        if (stateDragging) {\n          var offset = (getPageXY(e, horizontal) - statePageY) * scale;\n          var newTop = stateStartTop;\n          if (!isLTR && horizontal) {\n            newTop -= offset;\n          } else {\n            newTop += offset;\n          }\n          var tmpEnableScrollRange = enableScrollRangeRef.current;\n          var tmpEnableOffsetRange = enableOffsetRangeRef.current;\n          var ptg = tmpEnableOffsetRange ? newTop / tmpEnableOffsetRange : 0;\n          var newScrollTop = Math.ceil(ptg * tmpEnableScrollRange);\n          newScrollTop = Math.max(newScrollTop, 0);\n          newScrollTop = Math.min(newScrollTop, tmpEnableScrollRange);\n          moveRafId = raf(function () {\n            onScroll(newScrollTop, horizontal);\n          });\n        }\n      };\n      var onMouseUp = function onMouseUp() {\n        setDragging(false);\n        onStopMove();\n      };\n      window.addEventListener('mousemove', onMouseMove, {\n        passive: true\n      });\n      window.addEventListener('touchmove', onMouseMove, {\n        passive: true\n      });\n      window.addEventListener('mouseup', onMouseUp, {\n        passive: true\n      });\n      window.addEventListener('touchend', onMouseUp, {\n        passive: true\n      });\n      return function () {\n        window.removeEventListener('mousemove', onMouseMove);\n        window.removeEventListener('touchmove', onMouseMove);\n        window.removeEventListener('mouseup', onMouseUp);\n        window.removeEventListener('touchend', onMouseUp);\n        raf.cancel(moveRafId);\n      };\n    }\n  }, [dragging]);\n  React.useEffect(function () {\n    delayHidden();\n    return function () {\n      clearTimeout(visibleTimeoutRef.current);\n    };\n  }, [scrollOffset]);\n\n  // ====================== Imperative ======================\n  React.useImperativeHandle(ref, function () {\n    return {\n      delayHidden: delayHidden\n    };\n  });\n\n  // ======================== Render ========================\n  var scrollbarPrefixCls = \"\".concat(prefixCls, \"-scrollbar\");\n  var containerStyle = {\n    position: 'absolute',\n    visibility: visible ? null : 'hidden'\n  };\n  var thumbStyle = {\n    position: 'absolute',\n    borderRadius: 99,\n    background: 'var(--rc-virtual-list-scrollbar-bg, rgba(0, 0, 0, 0.5))',\n    cursor: 'pointer',\n    userSelect: 'none'\n  };\n  if (horizontal) {\n    Object.assign(containerStyle, {\n      height: 8,\n      left: 0,\n      right: 0,\n      bottom: 0\n    });\n    Object.assign(thumbStyle, _defineProperty({\n      height: '100%',\n      width: spinSize\n    }, isLTR ? 'left' : 'right', top));\n  } else {\n    Object.assign(containerStyle, _defineProperty({\n      width: 8,\n      top: 0,\n      bottom: 0\n    }, isLTR ? 'right' : 'left', 0));\n    Object.assign(thumbStyle, {\n      width: '100%',\n      height: spinSize,\n      top: top\n    });\n  }\n  return /*#__PURE__*/React.createElement(\"div\", {\n    ref: scrollbarRef,\n    className: classNames(scrollbarPrefixCls, _defineProperty(_defineProperty(_defineProperty({}, \"\".concat(scrollbarPrefixCls, \"-horizontal\"), horizontal), \"\".concat(scrollbarPrefixCls, \"-vertical\"), !horizontal), \"\".concat(scrollbarPrefixCls, \"-visible\"), visible)),\n    style: _objectSpread(_objectSpread({}, containerStyle), style),\n    onMouseDown: onContainerMouseDown,\n    onMouseMove: delayHidden\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    ref: thumbRef,\n    className: classNames(\"\".concat(scrollbarPrefixCls, \"-thumb\"), _defineProperty({}, \"\".concat(scrollbarPrefixCls, \"-thumb-moving\"), dragging)),\n    style: _objectSpread(_objectSpread({}, thumbStyle), propsThumbStyle),\n    onMouseDown: onThumbMouseDown\n  }));\n});\nif (process.env.NODE_ENV !== 'production') {\n  ScrollBar.displayName = 'ScrollBar';\n}\nexport default ScrollBar;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}