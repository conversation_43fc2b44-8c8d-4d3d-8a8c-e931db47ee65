{"ast": null, "code": "var _jsxFileName = \"C:\\\\Owen_Zhang\\\\\\u91CF\\u5316\\u4EA4\\u6613\\u7CFB\\u7EDF\\\\frontend\\\\src\\\\pages\\\\Settings.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Card, Form, Input, Button, Space, Typography, Alert, Divider, Switch, message, Modal, Tag, Row, Col, Spin } from 'antd';\nimport { SettingOutlined, KeyOutlined, CheckCircleOutlined, ExclamationCircleOutlined, ReloadOutlined, SaveOutlined, EyeInvisibleOutlined, EyeTwoTone } from '@ant-design/icons';\n// import axios from 'axios';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst {\n  Title,\n  Text,\n  Paragraph\n} = Typography;\nconst {\n  confirm\n} = Modal;\nconst Settings = () => {\n  _s();\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [config, setConfig] = useState({\n    tushare_token: '',\n    deepseek_api_key: '',\n    enabled_features: {\n      ai_analysis: true,\n      realtime_data: true,\n      notifications: true\n    }\n  });\n  const [tokenStatus, setTokenStatus] = useState({\n    tushare: 'unconfigured',\n    deepseek: 'unconfigured'\n  });\n  useEffect(() => {\n    // 暂时使用本地存储，避免API调用问题\n    loadLocalConfiguration();\n  }, []);\n  const loadLocalConfiguration = () => {\n    try {\n      const savedConfig = localStorage.getItem('quantTradingConfig');\n      if (savedConfig) {\n        const parsedConfig = JSON.parse(savedConfig);\n        setConfig(parsedConfig);\n        form.setFieldsValue(parsedConfig);\n\n        // 检查token状态\n        if (parsedConfig.tushare_token) {\n          setTokenStatus(prev => ({\n            ...prev,\n            tushare: 'valid'\n          }));\n        }\n        if (parsedConfig.deepseek_api_key) {\n          setTokenStatus(prev => ({\n            ...prev,\n            deepseek: 'valid'\n          }));\n        }\n      }\n    } catch (error) {\n      console.error('加载本地配置失败:', error);\n      message.warning('加载本地配置失败，将使用默认配置');\n    }\n  };\n  const testToken = async tokenType => {\n    const values = form.getFieldsValue();\n    const token = tokenType === 'tushare' ? values.tushare_token : values.deepseek_api_key;\n    if (!token) {\n      message.warning(`请先输入${tokenType === 'tushare' ? 'Tushare' : 'DeepSeek'} Token`);\n      return;\n    }\n\n    // 暂时使用简单的格式验证，避免API调用问题\n    setTokenStatus(prev => ({\n      ...prev,\n      [tokenType]: 'testing'\n    }));\n    setTimeout(() => {\n      if (tokenType === 'tushare') {\n        // Tushare token通常是32位以上的字符串\n        if (token.length >= 32) {\n          setTokenStatus(prev => ({\n            ...prev,\n            [tokenType]: 'valid'\n          }));\n          message.success('Tushare Token格式验证通过');\n        } else {\n          setTokenStatus(prev => ({\n            ...prev,\n            [tokenType]: 'invalid'\n          }));\n          message.error('Tushare Token长度应至少32位');\n        }\n      } else {\n        // DeepSeek API key应以sk-开头\n        if (token.startsWith('sk-') && token.length > 10) {\n          setTokenStatus(prev => ({\n            ...prev,\n            [tokenType]: 'valid'\n          }));\n          message.success('DeepSeek API Key格式验证通过');\n        } else {\n          setTokenStatus(prev => ({\n            ...prev,\n            [tokenType]: 'invalid'\n          }));\n          message.error('DeepSeek API Key应以sk-开头');\n        }\n      }\n    }, 1000);\n  };\n  const saveConfiguration = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 暂时保存到本地存储\n      localStorage.setItem('quantTradingConfig', JSON.stringify(values));\n      setConfig(values);\n      message.success('配置已保存到本地存储');\n      message.info('注意：配置已保存到浏览器本地存储，如需持久化请手动备份');\n    } catch (error) {\n      console.error('保存配置失败:', error);\n      message.error('保存配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const resetConfiguration = () => {\n    confirm({\n      title: '确认重置配置',\n      icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 151,\n        columnNumber: 13\n      }, this),\n      content: '这将清除所有已保存的Token配置，确定要继续吗？',\n      okText: '确定',\n      cancelText: '取消',\n      onOk() {\n        form.resetFields();\n        setTokenStatus({\n          tushare: 'unconfigured',\n          deepseek: 'unconfigured'\n        });\n        message.success('配置已重置');\n      }\n    });\n  };\n  const getStatusTag = status => {\n    switch (status) {\n      case 'valid':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"success\",\n          icon: /*#__PURE__*/_jsxDEV(CheckCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 169,\n            columnNumber: 43\n          }, this),\n          children: \"\\u5DF2\\u9A8C\\u8BC1\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 16\n        }, this);\n      case 'invalid':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"error\",\n          icon: /*#__PURE__*/_jsxDEV(ExclamationCircleOutlined, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 41\n          }, this),\n          children: \"\\u9A8C\\u8BC1\\u5931\\u8D25\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 16\n        }, this);\n      case 'testing':\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"processing\",\n          icon: /*#__PURE__*/_jsxDEV(Spin, {\n            size: \"small\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 173,\n            columnNumber: 46\n          }, this),\n          children: \"\\u9A8C\\u8BC1\\u4E2D...\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 16\n        }, this);\n      default:\n        return /*#__PURE__*/_jsxDEV(Tag, {\n          color: \"default\",\n          children: \"\\u672A\\u914D\\u7F6E\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 175,\n          columnNumber: 16\n        }, this);\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      padding: '24px',\n      maxWidth: '1200px',\n      margin: '0 auto'\n    },\n    children: [/*#__PURE__*/_jsxDEV(Title, {\n      level: 2,\n      children: [/*#__PURE__*/_jsxDEV(SettingOutlined, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 182,\n        columnNumber: 9\n      }, this), \" \\u7CFB\\u7EDF\\u8BBE\\u7F6E\"]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 181,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"Token\\u914D\\u7F6E\\u8BF4\\u660E\",\n      description: \"\\u8BF7\\u914D\\u7F6E\\u76F8\\u5E94\\u7684API Token\\u4EE5\\u542F\\u7528\\u5B8C\\u6574\\u529F\\u80FD\\u3002\\u5F53\\u524D\\u7248\\u672C\\u4F7F\\u7528\\u672C\\u5730\\u5B58\\u50A8\\uFF0C\\u914D\\u7F6E\\u4EC5\\u4FDD\\u5B58\\u5728\\u6D4F\\u89C8\\u5668\\u4E2D\\u3002\",\n      type: \"info\",\n      showIcon: true,\n      style: {\n        marginBottom: '16px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Alert, {\n      message: \"\\u7B80\\u5316\\u7248\\u672C\\u63D0\\u793A\",\n      description: \"\\u5F53\\u524D\\u4E3A\\u7B80\\u5316\\u7248\\u672C\\uFF0C\\u4EC5\\u63D0\\u4F9B\\u683C\\u5F0F\\u9A8C\\u8BC1\\u3002\\u5B8C\\u6574\\u7684API\\u9A8C\\u8BC1\\u529F\\u80FD\\u9700\\u8981\\u540E\\u7AEF\\u670D\\u52A1\\u5B8C\\u5168\\u542F\\u52A8\\u540E\\u624D\\u80FD\\u4F7F\\u7528\\u3002\",\n      type: \"warning\",\n      showIcon: true,\n      style: {\n        marginBottom: '24px'\n      }\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Spin, {\n      spinning: loading,\n      children: /*#__PURE__*/_jsxDEV(Form, {\n        form: form,\n        layout: \"vertical\",\n        initialValues: config,\n        onFinish: saveConfiguration,\n        children: [/*#__PURE__*/_jsxDEV(Row, {\n          gutter: [24, 24],\n          children: [/*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(KeyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 21\n                }, this), \"Tushare Pro\\u914D\\u7F6E\", getStatusTag(tokenStatus.tushare)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 213,\n                columnNumber: 19\n              }, this),\n              extra: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                onClick: () => testToken('tushare'),\n                loading: tokenStatus.tushare === 'testing',\n                children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 220,\n                columnNumber: 19\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"tushare_token\",\n                label: \"Tushare Token\",\n                rules: [{\n                  required: true,\n                  message: '请输入Tushare Token'\n                }, {\n                  min: 32,\n                  message: 'Token长度至少32位'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165Tushare Pro Token\",\n                  iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 57\n                  }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 239,\n                    columnNumber: 74\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 237,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: [\"\\u7528\\u4E8E\\u83B7\\u53D6\\u80A1\\u7968\\u6570\\u636E\\u548C\\u884C\\u60C5\\u4FE1\\u606F\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 245,\n                  columnNumber: 19\n                }, this), \"\\u83B7\\u53D6\\u65B9\\u5F0F: \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://tushare.pro/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"tushare.pro\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 28\n                }, this), \" \\u6CE8\\u518C\\u5E76\\u83B7\\u53D6Token\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 243,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 211,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 210,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Col, {\n            xs: 24,\n            lg: 12,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              title: /*#__PURE__*/_jsxDEV(Space, {\n                children: [/*#__PURE__*/_jsxDEV(KeyOutlined, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 256,\n                  columnNumber: 21\n                }, this), \"DeepSeek AI\\u914D\\u7F6E\", getStatusTag(tokenStatus.deepseek)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 255,\n                columnNumber: 19\n              }, this),\n              extra: /*#__PURE__*/_jsxDEV(Button, {\n                size: \"small\",\n                onClick: () => testToken('deepseek'),\n                loading: tokenStatus.deepseek === 'testing',\n                children: \"\\u6D4B\\u8BD5\\u8FDE\\u63A5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 262,\n                columnNumber: 19\n              }, this),\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: \"deepseek_api_key\",\n                label: \"DeepSeek API Key\",\n                rules: [{\n                  required: true,\n                  message: '请输入DeepSeek API Key'\n                }, {\n                  pattern: /^sk-/,\n                  message: 'API Key应以sk-开头'\n                }],\n                children: /*#__PURE__*/_jsxDEV(Input.Password, {\n                  placeholder: \"\\u8BF7\\u8F93\\u5165DeepSeek API Key\",\n                  iconRender: visible => visible ? /*#__PURE__*/_jsxDEV(EyeTwoTone, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 57\n                  }, this) : /*#__PURE__*/_jsxDEV(EyeInvisibleOutlined, {}, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 281,\n                    columnNumber: 74\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 279,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 271,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Paragraph, {\n                type: \"secondary\",\n                style: {\n                  fontSize: '12px'\n                },\n                children: [\"\\u7528\\u4E8EAI\\u667A\\u80FD\\u5206\\u6790\\u548C\\u5E02\\u573A\\u9884\\u6D4B\\u3002\", /*#__PURE__*/_jsxDEV(\"br\", {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 287,\n                  columnNumber: 19\n                }, this), \"\\u83B7\\u53D6\\u65B9\\u5F0F: \\u8BBF\\u95EE \", /*#__PURE__*/_jsxDEV(\"a\", {\n                  href: \"https://platform.deepseek.com/\",\n                  target: \"_blank\",\n                  rel: \"noopener noreferrer\",\n                  children: \"platform.deepseek.com\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 288,\n                  columnNumber: 28\n                }, this), \" \\u83B7\\u53D6API Key\"]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 285,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 253,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 252,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 208,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 294,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"\\u529F\\u80FD\\u8BBE\\u7F6E\",\n          style: {\n            marginTop: '24px'\n          },\n          children: /*#__PURE__*/_jsxDEV(Row, {\n            gutter: [24, 16],\n            children: [/*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: ['enabled_features', 'ai_analysis'],\n                label: \"AI\\u667A\\u80FD\\u5206\\u6790\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 305,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 300,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u542F\\u7528AI\\u5E02\\u573A\\u5206\\u6790\\u548C\\u9884\\u6D4B\\u529F\\u80FD\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 307,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 299,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: ['enabled_features', 'realtime_data'],\n                label: \"\\u5B9E\\u65F6\\u6570\\u636E\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 316,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 311,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u542F\\u7528\\u5B9E\\u65F6\\u884C\\u60C5\\u6570\\u636E\\u63A8\\u9001\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 318,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 310,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Col, {\n              xs: 24,\n              sm: 8,\n              children: [/*#__PURE__*/_jsxDEV(Form.Item, {\n                name: ['enabled_features', 'notifications'],\n                label: \"\\u6D88\\u606F\\u901A\\u77E5\",\n                valuePropName: \"checked\",\n                children: /*#__PURE__*/_jsxDEV(Switch, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 327,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 322,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Text, {\n                type: \"secondary\",\n                children: \"\\u542F\\u7528\\u7CFB\\u7EDF\\u6D88\\u606F\\u548C\\u544A\\u8B66\\u901A\\u77E5\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 329,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 321,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 298,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 297,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          style: {\n            marginTop: '24px',\n            textAlign: 'center'\n          },\n          children: /*#__PURE__*/_jsxDEV(Space, {\n            size: \"large\",\n            children: [/*#__PURE__*/_jsxDEV(Button, {\n              type: \"primary\",\n              icon: /*#__PURE__*/_jsxDEV(SaveOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 339,\n                columnNumber: 23\n              }, this),\n              htmlType: \"submit\",\n              loading: loading,\n              size: \"large\",\n              children: \"\\u4FDD\\u5B58\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              icon: /*#__PURE__*/_jsxDEV(ReloadOutlined, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 348,\n                columnNumber: 23\n              }, this),\n              onClick: loadLocalConfiguration,\n              size: \"large\",\n              children: \"\\u91CD\\u65B0\\u52A0\\u8F7D\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Button, {\n              danger: true,\n              onClick: resetConfiguration,\n              size: \"large\",\n              children: \"\\u91CD\\u7F6E\\u914D\\u7F6E\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 355,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 336,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 335,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 202,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 201,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 180,\n    columnNumber: 5\n  }, this);\n};\n_s(Settings, \"314DCnZUKJgWgByw9m6YC/L8XFo=\", false, function () {\n  return [Form.useForm];\n});\n_c = Settings;\nexport default Settings;\nvar _c;\n$RefreshReg$(_c, \"Settings\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Card", "Form", "Input", "<PERSON><PERSON>", "Space", "Typography", "<PERSON><PERSON>", "Divider", "Switch", "message", "Modal", "Tag", "Row", "Col", "Spin", "SettingOutlined", "KeyOutlined", "CheckCircleOutlined", "ExclamationCircleOutlined", "ReloadOutlined", "SaveOutlined", "EyeInvisibleOutlined", "EyeTwoTone", "jsxDEV", "_jsxDEV", "Title", "Text", "Paragraph", "confirm", "Settings", "_s", "form", "useForm", "loading", "setLoading", "config", "setConfig", "tushare_token", "deepseek_api_key", "enabled_features", "ai_analysis", "realtime_data", "notifications", "tokenStatus", "setTokenStatus", "tushare", "deepseek", "loadLocalConfiguration", "savedConfig", "localStorage", "getItem", "parsedConfig", "JSON", "parse", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prev", "error", "console", "warning", "testToken", "tokenType", "values", "getFieldsValue", "token", "setTimeout", "length", "success", "startsWith", "saveConfiguration", "validateFields", "setItem", "stringify", "info", "resetConfiguration", "title", "icon", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "content", "okText", "cancelText", "onOk", "resetFields", "getStatusTag", "status", "color", "children", "size", "style", "padding", "max<PERSON><PERSON><PERSON>", "margin", "level", "description", "type", "showIcon", "marginBottom", "spinning", "layout", "initialValues", "onFinish", "gutter", "xs", "lg", "extra", "onClick", "<PERSON><PERSON>", "name", "label", "rules", "required", "min", "Password", "placeholder", "iconRender", "visible", "fontSize", "href", "target", "rel", "pattern", "marginTop", "sm", "valuePropName", "textAlign", "htmlType", "danger", "_c", "$RefreshReg$"], "sources": ["C:/<PERSON>_<PERSON>/量化交易系统/frontend/src/pages/Settings.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport {\n  Card,\n  Form,\n  Input,\n  Button,\n  Space,\n  Typography,\n  Alert,\n  Divider,\n  Switch,\n  message,\n  Modal,\n  Tag,\n  Row,\n  Col,\n  Spin\n} from 'antd';\nimport {\n  SettingOutlined,\n  KeyOutlined,\n  CheckCircleOutlined,\n  ExclamationCircleOutlined,\n  ReloadOutlined,\n  SaveOutlined,\n  EyeInvisibleOutlined,\n  EyeTwoTone\n} from '@ant-design/icons';\n// import axios from 'axios';\n\nconst { Title, Text, Paragraph } = Typography;\nconst { confirm } = Modal;\n\ninterface TokenConfig {\n  tushare_token: string;\n  deepseek_api_key: string;\n  enabled_features: {\n    ai_analysis: boolean;\n    realtime_data: boolean;\n    notifications: boolean;\n  };\n}\n\ninterface TokenStatus {\n  tushare: 'valid' | 'invalid' | 'unconfigured' | 'testing';\n  deepseek: 'valid' | 'invalid' | 'unconfigured' | 'testing';\n}\n\nconst Settings: React.FC = () => {\n  const [form] = Form.useForm();\n  const [loading, setLoading] = useState(false);\n  const [config, setConfig] = useState<TokenConfig>({\n    tushare_token: '',\n    deepseek_api_key: '',\n    enabled_features: {\n      ai_analysis: true,\n      realtime_data: true,\n      notifications: true\n    }\n  });\n  const [tokenStatus, setTokenStatus] = useState<TokenStatus>({\n    tushare: 'unconfigured',\n    deepseek: 'unconfigured'\n  });\n\n  useEffect(() => {\n    // 暂时使用本地存储，避免API调用问题\n    loadLocalConfiguration();\n  }, []);\n\n  const loadLocalConfiguration = () => {\n    try {\n      const savedConfig = localStorage.getItem('quantTradingConfig');\n      if (savedConfig) {\n        const parsedConfig = JSON.parse(savedConfig);\n        setConfig(parsedConfig);\n        form.setFieldsValue(parsedConfig);\n\n        // 检查token状态\n        if (parsedConfig.tushare_token) {\n          setTokenStatus(prev => ({ ...prev, tushare: 'valid' }));\n        }\n        if (parsedConfig.deepseek_api_key) {\n          setTokenStatus(prev => ({ ...prev, deepseek: 'valid' }));\n        }\n      }\n    } catch (error) {\n      console.error('加载本地配置失败:', error);\n      message.warning('加载本地配置失败，将使用默认配置');\n    }\n  };\n\n  const testToken = async (tokenType: 'tushare' | 'deepseek') => {\n    const values = form.getFieldsValue();\n    const token = tokenType === 'tushare' ? values.tushare_token : values.deepseek_api_key;\n\n    if (!token) {\n      message.warning(`请先输入${tokenType === 'tushare' ? 'Tushare' : 'DeepSeek'} Token`);\n      return;\n    }\n\n    // 暂时使用简单的格式验证，避免API调用问题\n    setTokenStatus(prev => ({ ...prev, [tokenType]: 'testing' }));\n\n    setTimeout(() => {\n      if (tokenType === 'tushare') {\n        // Tushare token通常是32位以上的字符串\n        if (token.length >= 32) {\n          setTokenStatus(prev => ({ ...prev, [tokenType]: 'valid' }));\n          message.success('Tushare Token格式验证通过');\n        } else {\n          setTokenStatus(prev => ({ ...prev, [tokenType]: 'invalid' }));\n          message.error('Tushare Token长度应至少32位');\n        }\n      } else {\n        // DeepSeek API key应以sk-开头\n        if (token.startsWith('sk-') && token.length > 10) {\n          setTokenStatus(prev => ({ ...prev, [tokenType]: 'valid' }));\n          message.success('DeepSeek API Key格式验证通过');\n        } else {\n          setTokenStatus(prev => ({ ...prev, [tokenType]: 'invalid' }));\n          message.error('DeepSeek API Key应以sk-开头');\n        }\n      }\n    }, 1000);\n  };\n\n  const saveConfiguration = async () => {\n    try {\n      const values = await form.validateFields();\n      setLoading(true);\n\n      // 暂时保存到本地存储\n      localStorage.setItem('quantTradingConfig', JSON.stringify(values));\n      setConfig(values);\n\n      message.success('配置已保存到本地存储');\n      message.info('注意：配置已保存到浏览器本地存储，如需持久化请手动备份');\n\n    } catch (error) {\n      console.error('保存配置失败:', error);\n      message.error('保存配置失败');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const resetConfiguration = () => {\n    confirm({\n      title: '确认重置配置',\n      icon: <ExclamationCircleOutlined />,\n      content: '这将清除所有已保存的Token配置，确定要继续吗？',\n      okText: '确定',\n      cancelText: '取消',\n      onOk() {\n        form.resetFields();\n        setTokenStatus({\n          tushare: 'unconfigured',\n          deepseek: 'unconfigured'\n        });\n        message.success('配置已重置');\n      },\n    });\n  };\n\n  const getStatusTag = (status: string) => {\n    switch (status) {\n      case 'valid':\n        return <Tag color=\"success\" icon={<CheckCircleOutlined />}>已验证</Tag>;\n      case 'invalid':\n        return <Tag color=\"error\" icon={<ExclamationCircleOutlined />}>验证失败</Tag>;\n      case 'testing':\n        return <Tag color=\"processing\" icon={<Spin size=\"small\" />}>验证中...</Tag>;\n      default:\n        return <Tag color=\"default\">未配置</Tag>;\n    }\n  };\n\n  return (\n    <div style={{ padding: '24px', maxWidth: '1200px', margin: '0 auto' }}>\n      <Title level={2}>\n        <SettingOutlined /> 系统设置\n      </Title>\n      \n      <Alert\n        message=\"Token配置说明\"\n        description=\"请配置相应的API Token以启用完整功能。当前版本使用本地存储，配置仅保存在浏览器中。\"\n        type=\"info\"\n        showIcon\n        style={{ marginBottom: '16px' }}\n      />\n\n      <Alert\n        message=\"简化版本提示\"\n        description=\"当前为简化版本，仅提供格式验证。完整的API验证功能需要后端服务完全启动后才能使用。\"\n        type=\"warning\"\n        showIcon\n        style={{ marginBottom: '24px' }}\n      />\n\n      <Spin spinning={loading}>\n        <Form\n          form={form}\n          layout=\"vertical\"\n          initialValues={config}\n          onFinish={saveConfiguration}\n        >\n          <Row gutter={[24, 24]}>\n            {/* Tushare配置 */}\n            <Col xs={24} lg={12}>\n              <Card\n                title={\n                  <Space>\n                    <KeyOutlined />\n                    Tushare Pro配置\n                    {getStatusTag(tokenStatus.tushare)}\n                  </Space>\n                }\n                extra={\n                  <Button\n                    size=\"small\"\n                    onClick={() => testToken('tushare')}\n                    loading={tokenStatus.tushare === 'testing'}\n                  >\n                    测试连接\n                  </Button>\n                }\n              >\n                <Form.Item\n                  name=\"tushare_token\"\n                  label=\"Tushare Token\"\n                  rules={[\n                    { required: true, message: '请输入Tushare Token' },\n                    { min: 32, message: 'Token长度至少32位' }\n                  ]}\n                >\n                  <Input.Password\n                    placeholder=\"请输入Tushare Pro Token\"\n                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                  />\n                </Form.Item>\n                \n                <Paragraph type=\"secondary\" style={{ fontSize: '12px' }}>\n                  用于获取股票数据和行情信息。\n                  <br />\n                  获取方式: 访问 <a href=\"https://tushare.pro/\" target=\"_blank\" rel=\"noopener noreferrer\">tushare.pro</a> 注册并获取Token\n                </Paragraph>\n              </Card>\n            </Col>\n\n            {/* DeepSeek配置 */}\n            <Col xs={24} lg={12}>\n              <Card\n                title={\n                  <Space>\n                    <KeyOutlined />\n                    DeepSeek AI配置\n                    {getStatusTag(tokenStatus.deepseek)}\n                  </Space>\n                }\n                extra={\n                  <Button\n                    size=\"small\"\n                    onClick={() => testToken('deepseek')}\n                    loading={tokenStatus.deepseek === 'testing'}\n                  >\n                    测试连接\n                  </Button>\n                }\n              >\n                <Form.Item\n                  name=\"deepseek_api_key\"\n                  label=\"DeepSeek API Key\"\n                  rules={[\n                    { required: true, message: '请输入DeepSeek API Key' },\n                    { pattern: /^sk-/, message: 'API Key应以sk-开头' }\n                  ]}\n                >\n                  <Input.Password\n                    placeholder=\"请输入DeepSeek API Key\"\n                    iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}\n                  />\n                </Form.Item>\n                \n                <Paragraph type=\"secondary\" style={{ fontSize: '12px' }}>\n                  用于AI智能分析和市场预测。\n                  <br />\n                  获取方式: 访问 <a href=\"https://platform.deepseek.com/\" target=\"_blank\" rel=\"noopener noreferrer\">platform.deepseek.com</a> 获取API Key\n                </Paragraph>\n              </Card>\n            </Col>\n          </Row>\n\n          <Divider />\n\n          {/* 功能开关 */}\n          <Card title=\"功能设置\" style={{ marginTop: '24px' }}>\n            <Row gutter={[24, 16]}>\n              <Col xs={24} sm={8}>\n                <Form.Item\n                  name={['enabled_features', 'ai_analysis']}\n                  label=\"AI智能分析\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n                <Text type=\"secondary\">启用AI市场分析和预测功能</Text>\n              </Col>\n              \n              <Col xs={24} sm={8}>\n                <Form.Item\n                  name={['enabled_features', 'realtime_data']}\n                  label=\"实时数据\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n                <Text type=\"secondary\">启用实时行情数据推送</Text>\n              </Col>\n              \n              <Col xs={24} sm={8}>\n                <Form.Item\n                  name={['enabled_features', 'notifications']}\n                  label=\"消息通知\"\n                  valuePropName=\"checked\"\n                >\n                  <Switch />\n                </Form.Item>\n                <Text type=\"secondary\">启用系统消息和告警通知</Text>\n              </Col>\n            </Row>\n          </Card>\n\n          {/* 操作按钮 */}\n          <Card style={{ marginTop: '24px', textAlign: 'center' }}>\n            <Space size=\"large\">\n              <Button\n                type=\"primary\"\n                icon={<SaveOutlined />}\n                htmlType=\"submit\"\n                loading={loading}\n                size=\"large\"\n              >\n                保存配置\n              </Button>\n              \n              <Button\n                icon={<ReloadOutlined />}\n                onClick={loadLocalConfiguration}\n                size=\"large\"\n              >\n                重新加载\n              </Button>\n              \n              <Button\n                danger\n                onClick={resetConfiguration}\n                size=\"large\"\n              >\n                重置配置\n              </Button>\n            </Space>\n          </Card>\n        </Form>\n      </Spin>\n    </div>\n  );\n};\n\nexport default Settings;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,MAAM,EACNC,KAAK,EACLC,UAAU,EACVC,KAAK,EACLC,OAAO,EACPC,MAAM,EACNC,OAAO,EACPC,KAAK,EACLC,GAAG,EACHC,GAAG,EACHC,GAAG,EACHC,IAAI,QACC,MAAM;AACb,SACEC,eAAe,EACfC,WAAW,EACXC,mBAAmB,EACnBC,yBAAyB,EACzBC,cAAc,EACdC,YAAY,EACZC,oBAAoB,EACpBC,UAAU,QACL,mBAAmB;AAC1B;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAEA,MAAM;EAAEC,KAAK;EAAEC,IAAI;EAAEC;AAAU,CAAC,GAAGtB,UAAU;AAC7C,MAAM;EAAEuB;AAAQ,CAAC,GAAGlB,KAAK;AAiBzB,MAAMmB,QAAkB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC/B,MAAM,CAACC,IAAI,CAAC,GAAG9B,IAAI,CAAC+B,OAAO,CAAC,CAAC;EAC7B,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGpC,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACqC,MAAM,EAAEC,SAAS,CAAC,GAAGtC,QAAQ,CAAc;IAChDuC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBC,gBAAgB,EAAE;MAChBC,WAAW,EAAE,IAAI;MACjBC,aAAa,EAAE,IAAI;MACnBC,aAAa,EAAE;IACjB;EACF,CAAC,CAAC;EACF,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAG9C,QAAQ,CAAc;IAC1D+C,OAAO,EAAE,cAAc;IACvBC,QAAQ,EAAE;EACZ,CAAC,CAAC;EAEF/C,SAAS,CAAC,MAAM;IACd;IACAgD,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,sBAAsB,GAAGA,CAAA,KAAM;IACnC,IAAI;MACF,MAAMC,WAAW,GAAGC,YAAY,CAACC,OAAO,CAAC,oBAAoB,CAAC;MAC9D,IAAIF,WAAW,EAAE;QACf,MAAMG,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACL,WAAW,CAAC;QAC5CZ,SAAS,CAACe,YAAY,CAAC;QACvBpB,IAAI,CAACuB,cAAc,CAACH,YAAY,CAAC;;QAEjC;QACA,IAAIA,YAAY,CAACd,aAAa,EAAE;UAC9BO,cAAc,CAACW,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAEV,OAAO,EAAE;UAAQ,CAAC,CAAC,CAAC;QACzD;QACA,IAAIM,YAAY,CAACb,gBAAgB,EAAE;UACjCM,cAAc,CAACW,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAET,QAAQ,EAAE;UAAQ,CAAC,CAAC,CAAC;QAC1D;MACF;IACF,CAAC,CAAC,OAAOU,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,WAAW,EAAEA,KAAK,CAAC;MACjC/C,OAAO,CAACiD,OAAO,CAAC,kBAAkB,CAAC;IACrC;EACF,CAAC;EAED,MAAMC,SAAS,GAAG,MAAOC,SAAiC,IAAK;IAC7D,MAAMC,MAAM,GAAG9B,IAAI,CAAC+B,cAAc,CAAC,CAAC;IACpC,MAAMC,KAAK,GAAGH,SAAS,KAAK,SAAS,GAAGC,MAAM,CAACxB,aAAa,GAAGwB,MAAM,CAACvB,gBAAgB;IAEtF,IAAI,CAACyB,KAAK,EAAE;MACVtD,OAAO,CAACiD,OAAO,CAAC,OAAOE,SAAS,KAAK,SAAS,GAAG,SAAS,GAAG,UAAU,QAAQ,CAAC;MAChF;IACF;;IAEA;IACAhB,cAAc,CAACW,IAAI,KAAK;MAAE,GAAGA,IAAI;MAAE,CAACK,SAAS,GAAG;IAAU,CAAC,CAAC,CAAC;IAE7DI,UAAU,CAAC,MAAM;MACf,IAAIJ,SAAS,KAAK,SAAS,EAAE;QAC3B;QACA,IAAIG,KAAK,CAACE,MAAM,IAAI,EAAE,EAAE;UACtBrB,cAAc,CAACW,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACK,SAAS,GAAG;UAAQ,CAAC,CAAC,CAAC;UAC3DnD,OAAO,CAACyD,OAAO,CAAC,qBAAqB,CAAC;QACxC,CAAC,MAAM;UACLtB,cAAc,CAACW,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACK,SAAS,GAAG;UAAU,CAAC,CAAC,CAAC;UAC7DnD,OAAO,CAAC+C,KAAK,CAAC,uBAAuB,CAAC;QACxC;MACF,CAAC,MAAM;QACL;QACA,IAAIO,KAAK,CAACI,UAAU,CAAC,KAAK,CAAC,IAAIJ,KAAK,CAACE,MAAM,GAAG,EAAE,EAAE;UAChDrB,cAAc,CAACW,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACK,SAAS,GAAG;UAAQ,CAAC,CAAC,CAAC;UAC3DnD,OAAO,CAACyD,OAAO,CAAC,wBAAwB,CAAC;QAC3C,CAAC,MAAM;UACLtB,cAAc,CAACW,IAAI,KAAK;YAAE,GAAGA,IAAI;YAAE,CAACK,SAAS,GAAG;UAAU,CAAC,CAAC,CAAC;UAC7DnD,OAAO,CAAC+C,KAAK,CAAC,yBAAyB,CAAC;QAC1C;MACF;IACF,CAAC,EAAE,IAAI,CAAC;EACV,CAAC;EAED,MAAMY,iBAAiB,GAAG,MAAAA,CAAA,KAAY;IACpC,IAAI;MACF,MAAMP,MAAM,GAAG,MAAM9B,IAAI,CAACsC,cAAc,CAAC,CAAC;MAC1CnC,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACAe,YAAY,CAACqB,OAAO,CAAC,oBAAoB,EAAElB,IAAI,CAACmB,SAAS,CAACV,MAAM,CAAC,CAAC;MAClEzB,SAAS,CAACyB,MAAM,CAAC;MAEjBpD,OAAO,CAACyD,OAAO,CAAC,YAAY,CAAC;MAC7BzD,OAAO,CAAC+D,IAAI,CAAC,6BAA6B,CAAC;IAE7C,CAAC,CAAC,OAAOhB,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,SAAS,EAAEA,KAAK,CAAC;MAC/B/C,OAAO,CAAC+C,KAAK,CAAC,QAAQ,CAAC;IACzB,CAAC,SAAS;MACRtB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMuC,kBAAkB,GAAGA,CAAA,KAAM;IAC/B7C,OAAO,CAAC;MACN8C,KAAK,EAAE,QAAQ;MACfC,IAAI,eAAEnD,OAAA,CAACN,yBAAyB;QAAA0D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;MACnCC,OAAO,EAAE,2BAA2B;MACpCC,MAAM,EAAE,IAAI;MACZC,UAAU,EAAE,IAAI;MAChBC,IAAIA,CAAA,EAAG;QACLpD,IAAI,CAACqD,WAAW,CAAC,CAAC;QAClBxC,cAAc,CAAC;UACbC,OAAO,EAAE,cAAc;UACvBC,QAAQ,EAAE;QACZ,CAAC,CAAC;QACFrC,OAAO,CAACyD,OAAO,CAAC,OAAO,CAAC;MAC1B;IACF,CAAC,CAAC;EACJ,CAAC;EAED,MAAMmB,YAAY,GAAIC,MAAc,IAAK;IACvC,QAAQA,MAAM;MACZ,KAAK,OAAO;QACV,oBAAO9D,OAAA,CAACb,GAAG;UAAC4E,KAAK,EAAC,SAAS;UAACZ,IAAI,eAAEnD,OAAA,CAACP,mBAAmB;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAS,QAAA,EAAC;QAAG;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MACtE,KAAK,SAAS;QACZ,oBAAOvD,OAAA,CAACb,GAAG;UAAC4E,KAAK,EAAC,OAAO;UAACZ,IAAI,eAAEnD,OAAA,CAACN,yBAAyB;YAAA0D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAS,QAAA,EAAC;QAAI;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC3E,KAAK,SAAS;QACZ,oBAAOvD,OAAA,CAACb,GAAG;UAAC4E,KAAK,EAAC,YAAY;UAACZ,IAAI,eAAEnD,OAAA,CAACV,IAAI;YAAC2E,IAAI,EAAC;UAAO;YAAAb,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAS,QAAA,EAAC;QAAM;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAC1E;QACE,oBAAOvD,OAAA,CAACb,GAAG;UAAC4E,KAAK,EAAC,SAAS;UAAAC,QAAA,EAAC;QAAG;UAAAZ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;IACzC;EACF,CAAC;EAED,oBACEvD,OAAA;IAAKkE,KAAK,EAAE;MAAEC,OAAO,EAAE,MAAM;MAAEC,QAAQ,EAAE,QAAQ;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAL,QAAA,gBACpEhE,OAAA,CAACC,KAAK;MAACqE,KAAK,EAAE,CAAE;MAAAN,QAAA,gBACdhE,OAAA,CAACT,eAAe;QAAA6D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,6BACrB;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAO,CAAC,eAERvD,OAAA,CAAClB,KAAK;MACJG,OAAO,EAAC,+BAAW;MACnBsF,WAAW,EAAC,mOAA+C;MAC3DC,IAAI,EAAC,MAAM;MACXC,QAAQ;MACRP,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAO;IAAE;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFvD,OAAA,CAAClB,KAAK;MACJG,OAAO,EAAC,sCAAQ;MAChBsF,WAAW,EAAC,+OAA4C;MACxDC,IAAI,EAAC,SAAS;MACdC,QAAQ;MACRP,KAAK,EAAE;QAAEQ,YAAY,EAAE;MAAO;IAAE;MAAAtB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjC,CAAC,eAEFvD,OAAA,CAACV,IAAI;MAACqF,QAAQ,EAAElE,OAAQ;MAAAuD,QAAA,eACtBhE,OAAA,CAACvB,IAAI;QACH8B,IAAI,EAAEA,IAAK;QACXqE,MAAM,EAAC,UAAU;QACjBC,aAAa,EAAElE,MAAO;QACtBmE,QAAQ,EAAElC,iBAAkB;QAAAoB,QAAA,gBAE5BhE,OAAA,CAACZ,GAAG;UAAC2F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;UAAAf,QAAA,gBAEpBhE,OAAA,CAACX,GAAG;YAAC2F,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAjB,QAAA,eAClBhE,OAAA,CAACxB,IAAI;cACH0E,KAAK,eACHlD,OAAA,CAACpB,KAAK;gBAAAoF,QAAA,gBACJhE,OAAA,CAACR,WAAW;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,2BAEf,EAACM,YAAY,CAAC1C,WAAW,CAACE,OAAO,CAAC;cAAA;gBAAA+B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC7B,CACR;cACD2B,KAAK,eACHlF,OAAA,CAACrB,MAAM;gBACLsF,IAAI,EAAC,OAAO;gBACZkB,OAAO,EAAEA,CAAA,KAAMhD,SAAS,CAAC,SAAS,CAAE;gBACpC1B,OAAO,EAAEU,WAAW,CAACE,OAAO,KAAK,SAAU;gBAAA2C,QAAA,EAC5C;cAED;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;cAAAS,QAAA,gBAEDhE,OAAA,CAACvB,IAAI,CAAC2G,IAAI;gBACRC,IAAI,EAAC,eAAe;gBACpBC,KAAK,EAAC,eAAe;gBACrBC,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAEvG,OAAO,EAAE;gBAAmB,CAAC,EAC/C;kBAAEwG,GAAG,EAAE,EAAE;kBAAExG,OAAO,EAAE;gBAAe,CAAC,CACpC;gBAAA+E,QAAA,eAEFhE,OAAA,CAACtB,KAAK,CAACgH,QAAQ;kBACbC,WAAW,EAAC,qCAAsB;kBAClCC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAG7F,OAAA,CAACF,UAAU;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvD,OAAA,CAACH,oBAAoB;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAG;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZvD,OAAA,CAACG,SAAS;gBAACqE,IAAI,EAAC,WAAW;gBAACN,KAAK,EAAE;kBAAE4B,QAAQ,EAAE;gBAAO,CAAE;gBAAA9B,QAAA,GAAC,sFAEvD,eAAAhE,OAAA;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,2CACG,eAAAvD,OAAA;kBAAG+F,IAAI,EAAC,sBAAsB;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAAjC,QAAA,EAAC;gBAAW;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wCACnG;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC,eAGNvD,OAAA,CAACX,GAAG;YAAC2F,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,EAAG;YAAAjB,QAAA,eAClBhE,OAAA,CAACxB,IAAI;cACH0E,KAAK,eACHlD,OAAA,CAACpB,KAAK;gBAAAoF,QAAA,gBACJhE,OAAA,CAACR,WAAW;kBAAA4D,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,2BAEf,EAACM,YAAY,CAAC1C,WAAW,CAACG,QAAQ,CAAC;cAAA;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9B,CACR;cACD2B,KAAK,eACHlF,OAAA,CAACrB,MAAM;gBACLsF,IAAI,EAAC,OAAO;gBACZkB,OAAO,EAAEA,CAAA,KAAMhD,SAAS,CAAC,UAAU,CAAE;gBACrC1B,OAAO,EAAEU,WAAW,CAACG,QAAQ,KAAK,SAAU;gBAAA0C,QAAA,EAC7C;cAED;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CACT;cAAAS,QAAA,gBAEDhE,OAAA,CAACvB,IAAI,CAAC2G,IAAI;gBACRC,IAAI,EAAC,kBAAkB;gBACvBC,KAAK,EAAC,kBAAkB;gBACxBC,KAAK,EAAE,CACL;kBAAEC,QAAQ,EAAE,IAAI;kBAAEvG,OAAO,EAAE;gBAAsB,CAAC,EAClD;kBAAEiH,OAAO,EAAE,MAAM;kBAAEjH,OAAO,EAAE;gBAAiB,CAAC,CAC9C;gBAAA+E,QAAA,eAEFhE,OAAA,CAACtB,KAAK,CAACgH,QAAQ;kBACbC,WAAW,EAAC,oCAAqB;kBACjCC,UAAU,EAAGC,OAAO,IAAMA,OAAO,gBAAG7F,OAAA,CAACF,UAAU;oBAAAsD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE,CAAC,gBAAGvD,OAAA,CAACH,oBAAoB;oBAAAuD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAG;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChF;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACO,CAAC,eAEZvD,OAAA,CAACG,SAAS;gBAACqE,IAAI,EAAC,WAAW;gBAACN,KAAK,EAAE;kBAAE4B,QAAQ,EAAE;gBAAO,CAAE;gBAAA9B,QAAA,GAAC,4EAEvD,eAAAhE,OAAA;kBAAAoD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAK,CAAC,2CACG,eAAAvD,OAAA;kBAAG+F,IAAI,EAAC,gCAAgC;kBAACC,MAAM,EAAC,QAAQ;kBAACC,GAAG,EAAC,qBAAqB;kBAAAjC,QAAA,EAAC;gBAAqB;kBAAAZ,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,wBACvH;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAW,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvD,OAAA,CAACjB,OAAO;UAAAqE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAGXvD,OAAA,CAACxB,IAAI;UAAC0E,KAAK,EAAC,0BAAM;UAACgB,KAAK,EAAE;YAAEiC,SAAS,EAAE;UAAO,CAAE;UAAAnC,QAAA,eAC9ChE,OAAA,CAACZ,GAAG;YAAC2F,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,CAAE;YAAAf,QAAA,gBACpBhE,OAAA,CAACX,GAAG;cAAC2F,EAAE,EAAE,EAAG;cAACoB,EAAE,EAAE,CAAE;cAAApC,QAAA,gBACjBhE,OAAA,CAACvB,IAAI,CAAC2G,IAAI;gBACRC,IAAI,EAAE,CAAC,kBAAkB,EAAE,aAAa,CAAE;gBAC1CC,KAAK,EAAC,4BAAQ;gBACde,aAAa,EAAC,SAAS;gBAAArC,QAAA,eAEvBhE,OAAA,CAAChB,MAAM;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACZvD,OAAA,CAACE,IAAI;gBAACsE,IAAI,EAAC,WAAW;gBAAAR,QAAA,EAAC;cAAa;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxC,CAAC,eAENvD,OAAA,CAACX,GAAG;cAAC2F,EAAE,EAAE,EAAG;cAACoB,EAAE,EAAE,CAAE;cAAApC,QAAA,gBACjBhE,OAAA,CAACvB,IAAI,CAAC2G,IAAI;gBACRC,IAAI,EAAE,CAAC,kBAAkB,EAAE,eAAe,CAAE;gBAC5CC,KAAK,EAAC,0BAAM;gBACZe,aAAa,EAAC,SAAS;gBAAArC,QAAA,eAEvBhE,OAAA,CAAChB,MAAM;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACZvD,OAAA,CAACE,IAAI;gBAACsE,IAAI,EAAC,WAAW;gBAAAR,QAAA,EAAC;cAAU;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrC,CAAC,eAENvD,OAAA,CAACX,GAAG;cAAC2F,EAAE,EAAE,EAAG;cAACoB,EAAE,EAAE,CAAE;cAAApC,QAAA,gBACjBhE,OAAA,CAACvB,IAAI,CAAC2G,IAAI;gBACRC,IAAI,EAAE,CAAC,kBAAkB,EAAE,eAAe,CAAE;gBAC5CC,KAAK,EAAC,0BAAM;gBACZe,aAAa,EAAC,SAAS;gBAAArC,QAAA,eAEvBhE,OAAA,CAAChB,MAAM;kBAAAoE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CAAC,eACZvD,OAAA,CAACE,IAAI;gBAACsE,IAAI,EAAC,WAAW;gBAAAR,QAAA,EAAC;cAAW;gBAAAZ,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtC,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAGPvD,OAAA,CAACxB,IAAI;UAAC0F,KAAK,EAAE;YAAEiC,SAAS,EAAE,MAAM;YAAEG,SAAS,EAAE;UAAS,CAAE;UAAAtC,QAAA,eACtDhE,OAAA,CAACpB,KAAK;YAACqF,IAAI,EAAC,OAAO;YAAAD,QAAA,gBACjBhE,OAAA,CAACrB,MAAM;cACL6F,IAAI,EAAC,SAAS;cACdrB,IAAI,eAAEnD,OAAA,CAACJ,YAAY;gBAAAwD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACvBgD,QAAQ,EAAC,QAAQ;cACjB9F,OAAO,EAAEA,OAAQ;cACjBwD,IAAI,EAAC,OAAO;cAAAD,QAAA,EACb;YAED;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETvD,OAAA,CAACrB,MAAM;cACLwE,IAAI,eAAEnD,OAAA,CAACL,cAAc;gBAAAyD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE,CAAE;cACzB4B,OAAO,EAAE5D,sBAAuB;cAChC0C,IAAI,EAAC,OAAO;cAAAD,QAAA,EACb;YAED;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC,eAETvD,OAAA,CAACrB,MAAM;cACL6H,MAAM;cACNrB,OAAO,EAAElC,kBAAmB;cAC5BgB,IAAI,EAAC,OAAO;cAAAD,QAAA,EACb;YAED;cAAAZ,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAQ,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACJ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACjD,EAAA,CA/TID,QAAkB;EAAA,QACP5B,IAAI,CAAC+B,OAAO;AAAA;AAAAiG,EAAA,GADvBpG,QAAkB;AAiUxB,eAAeA,QAAQ;AAAC,IAAAoG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}