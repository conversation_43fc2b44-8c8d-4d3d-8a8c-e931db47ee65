<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>帮助支持 - 量化交易监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            line-height: 1.6;
        }

        .container {
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin-right: 20px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-2px);
        }

        .header h1 {
            font-size: 2em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-content {
            flex: 1;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .help-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 20px;
        }

        .help-section h2 {
            font-size: 1.3em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            color: #40a9ff;
        }

        .help-section h3 {
            font-size: 1.1em;
            margin: 15px 0 10px 0;
            color: #91d5ff;
        }

        .help-section p {
            margin-bottom: 15px;
            color: rgba(255, 255, 255, 0.9);
        }

        .help-section ul {
            margin-left: 20px;
            margin-bottom: 15px;
        }

        .help-section li {
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.9);
        }

        .code-block {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            border-left: 4px solid #1890ff;
            overflow-x: auto;
        }

        .highlight {
            background: rgba(24, 144, 255, 0.2);
            padding: 2px 6px;
            border-radius: 4px;
            color: #91d5ff;
        }

        .warning {
            background: rgba(250, 173, 20, 0.2);
            border: 1px solid rgba(250, 173, 20, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #ffe58f;
        }

        .success {
            background: rgba(82, 196, 26, 0.2);
            border: 1px solid rgba(82, 196, 26, 0.5);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #b7eb8f;
        }

        .link {
            color: #40a9ff;
            text-decoration: none;
            cursor: pointer;
        }

        .link:hover {
            text-decoration: underline;
        }

        .step-list {
            counter-reset: step-counter;
        }

        .step-list li {
            counter-increment: step-counter;
            position: relative;
            padding-left: 30px;
            margin-bottom: 15px;
        }

        .step-list li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background: #1890ff;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <button class="back-btn" id="back-btn">← 返回</button>
            <h1>❓ 帮助支持</h1>
        </header>

        <main class="main-content">
            <!-- 快速开始 -->
            <div class="help-section">
                <h2>🚀 快速开始</h2>
                <p>欢迎使用量化交易监控系统！按照以下步骤快速开始：</p>
                
                <ol class="step-list">
                    <li>点击<span class="highlight">系统配置</span>进入配置页面</li>
                    <li>输入Tushare Token并点击<span class="highlight">测试</span>验证</li>
                    <li>可选配置Wind或米筐数据源</li>
                    <li>点击<span class="highlight">保存配置</span>完成设置</li>
                    <li>返回主页面，点击<span class="highlight">启动系统</span></li>
                    <li>系统启动后会自动打开交易界面</li>
                </ol>

                <div class="success">
                    <strong>提示：</strong>首次使用建议先配置Tushare数据源，这是系统的主要数据来源。
                </div>
            </div>

            <!-- 数据源配置 -->
            <div class="help-section">
                <h2>📊 数据源配置</h2>
                
                <h3>Tushare Pro (必需)</h3>
                <p>主要的股票数据源，支持A股和港股数据。</p>
                <ul>
                    <li>访问 <span class="link" onclick="openLink('https://tushare.pro/')">https://tushare.pro/</span> 注册账户</li>
                    <li>登录后获取API Token</li>
                    <li>在配置页面输入Token并测试</li>
                </ul>

                <h3>万得 Wind (可选)</h3>
                <p>机构级数据质量，覆盖全市场。</p>
                <ul>
                    <li>需要购买Wind终端授权</li>
                    <li>安装WindPy: <span class="highlight">pip install WindPy</span></li>
                    <li>年费通常在数万元级别</li>
                </ul>

                <h3>米筐 RiceQuant (可选)</h3>
                <p>性价比较好的数据服务。</p>
                <ul>
                    <li>访问 <span class="link" onclick="openLink('https://www.ricequant.com/')">https://www.ricequant.com/</span> 注册</li>
                    <li>获取API Key</li>
                    <li>有免费额度，付费版价格合理</li>
                </ul>
            </div>

            <!-- 系统要求 -->
            <div class="help-section">
                <h2>💻 系统要求</h2>
                
                <h3>最低配置</h3>
                <ul>
                    <li>操作系统: Windows 10/11, macOS 10.14+, Ubuntu 18.04+</li>
                    <li>内存: 4GB RAM</li>
                    <li>存储: 2GB 可用空间</li>
                    <li>网络: 稳定的互联网连接</li>
                </ul>

                <h3>推荐配置</h3>
                <ul>
                    <li>内存: 8GB+ RAM</li>
                    <li>存储: SSD硬盘</li>
                    <li>网络: 宽带连接</li>
                    <li>显示器: 1920x1080 分辨率</li>
                </ul>

                <div class="warning">
                    <strong>注意：</strong>系统需要访问外部API，请确保网络连接稳定且防火墙允许应用访问网络。
                </div>
            </div>

            <!-- 常见问题 -->
            <div class="help-section">
                <h2>❓ 常见问题</h2>
                
                <h3>Q: Token验证失败怎么办？</h3>
                <p>A: 请检查以下几点：</p>
                <ul>
                    <li>确认Token格式正确（32位以上十六进制字符串）</li>
                    <li>检查网络连接是否正常</li>
                    <li>确认Token未过期且有足够的调用次数</li>
                    <li>尝试重新获取Token</li>
                </ul>

                <h3>Q: 服务启动失败？</h3>
                <p>A: 可能的解决方案：</p>
                <ul>
                    <li>检查端口8000和3001是否被占用</li>
                    <li>确认防火墙设置允许应用运行</li>
                    <li>重启应用程序</li>
                    <li>查看系统状态页面的错误日志</li>
                </ul>

                <h3>Q: 数据获取缓慢？</h3>
                <p>A: 优化建议：</p>
                <ul>
                    <li>检查网络连接速度</li>
                    <li>减少同时监控的股票数量</li>
                    <li>考虑升级到付费数据源</li>
                </ul>
            </div>

            <!-- 技术支持 -->
            <div class="help-section full-width">
                <h2>🛠️ 技术支持</h2>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h3>联系方式</h3>
                        <ul>
                            <li>邮箱: <EMAIL></li>
                            <li>QQ群: 123456789</li>
                            <li>微信群: 扫描二维码加入</li>
                            <li>工作时间: 周一至周五 9:00-18:00</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3>在线资源</h3>
                        <ul>
                            <li><span class="link" onclick="openLink('https://docs.quanttrading.com')">在线文档</span></li>
                            <li><span class="link" onclick="openLink('https://github.com/quanttrading/issues')">问题反馈</span></li>
                            <li><span class="link" onclick="openLink('https://www.quanttrading.com/tutorial')">视频教程</span></li>
                            <li><span class="link" onclick="openLink('https://forum.quanttrading.com')">用户论坛</span></li>
                        </ul>
                    </div>
                </div>

                <div class="code-block">
                    <strong>系统信息（用于技术支持）：</strong><br>
                    版本: <span id="app-version">v1.0.0</span><br>
                    平台: <span id="platform-info">Windows x64</span><br>
                    Electron: <span id="electron-version">27.0.0</span>
                </div>
            </div>
        </main>
    </div>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', async () => {
            // 获取应用信息
            try {
                const appInfo = await window.electronAPI.getAppInfo();
                document.getElementById('app-version').textContent = `v${appInfo.version}`;
                document.getElementById('platform-info').textContent = `${window.platform.os} ${window.platform.arch}`;
                document.getElementById('electron-version').textContent = window.versions.electron();
            } catch (error) {
                console.error('获取应用信息失败:', error);
            }
        });

        // 返回按钮
        document.getElementById('back-btn').addEventListener('click', () => {
            window.electronAPI.navigateTo('index');
        });

        // 打开外部链接
        function openLink(url) {
            window.electronAPI.openExternal(url);
        }
    </script>
</body>
</html>
