# 🚀 量化交易监控系统 - 启动指南

## 📋 系统概述

这是一个完整的量化交易监控系统，集成了数据获取、技术分析、AI智能分析、风险管理和实时监控功能。

### 🎯 核心功能
- ✅ **实时数据获取**: Tushare Pro API集成
- ✅ **技术指标分析**: MA、MACD、RSI、布林带、KDJ
- ✅ **AI智能分析**: DeepSeek API集成，情绪分析和风险识别
- ✅ **风险管理**: 多维度风险监控和告警
- ✅ **实时通信**: WebSocket实时数据推送
- ✅ **可视化界面**: React前端，专业图表展示

## 🛠️ 环境要求

### 必需软件
- **Python 3.8+** (推荐 3.10+)
- **Node.js 16+** (推荐 18+)
- **Git** (用于代码管理)

### 可选软件 (高级功能)
- **PostgreSQL 13+** (数据存储)
- **Redis 6+** (缓存)
- **InfluxDB 2.0+** (时序数据)

## 🚀 快速启动

### Windows 用户

1. **快速测试**
   ```bash
   # 双击运行或在命令行执行
   quick_test.bat
   ```

2. **启动系统**
   ```bash
   # 双击运行或在命令行执行
   start_system.bat
   ```

### Linux/Mac 用户

1. **快速测试**
   ```bash
   chmod +x *.sh
   ./quick_test.sh  # 如果有的话
   ```

2. **启动系统**
   ```bash
   ./start_system.sh
   ```

3. **停止系统**
   ```bash
   ./stop_system.sh
   ```

## 📊 访问地址

启动成功后，可以通过以下地址访问系统：

- 🌐 **前端界面**: http://localhost:3001
- 📊 **后端API**: http://localhost:8000
- 📚 **API文档**: http://localhost:8000/docs
- 🔍 **健康检查**: http://localhost:8000/health
- 📡 **WebSocket**: ws://localhost:8000/ws

## 🔧 配置说明

### API密钥配置

系统已预配置以下API密钥：

1. **Tushare Pro Token**: `772e043e24...d0f28d` ✅
2. **DeepSeek API Key**: `sk-165fc7c9...fa4006` ✅

### 环境变量

主要配置文件：
- `backend/.env` - 后端环境配置
- `frontend/.env` - 前端环境配置

## 📱 功能模块

### 1. 数据管理
- 股票基本信息管理
- 实时行情数据获取
- 历史数据回测

### 2. 技术分析
- 多种技术指标计算
- 交易信号生成
- 策略回测

### 3. AI分析
- 市场情绪分析
- 风险事件识别
- 智能投资建议

### 4. 风险管理
- 实时风险监控
- 多级风险告警
- 持仓管理

### 5. 实时监控
- WebSocket实时数据
- 系统状态监控
- 性能指标展示

## 🐛 故障排除

### 常见问题

1. **Python依赖安装失败**
   ```bash
   # 升级pip
   python -m pip install --upgrade pip
   
   # 使用国内镜像
   pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
   ```

2. **Node.js依赖安装失败**
   ```bash
   # 清理缓存
   npm cache clean --force
   
   # 使用国内镜像
   npm install --registry https://registry.npmmirror.com
   ```

3. **端口占用问题**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :8000
   netstat -ano | findstr :3001
   
   # 杀死占用进程
   taskkill /PID <进程ID> /F
   ```

4. **API连接失败**
   - 检查网络连接
   - 验证API密钥是否正确
   - 查看日志文件 `backend/logs/`

### 日志文件

- 后端日志: `backend/logs/backend.log`
- 前端日志: `backend/logs/frontend.log`
- 系统日志: `backend/logs/quant_trading.log`

## 📞 技术支持

如遇到问题，请：

1. 查看日志文件
2. 运行快速测试脚本
3. 检查环境配置
4. 参考故障排除指南

## 🎉 开始使用

1. 运行 `start_system.bat` (Windows) 或 `./start_system.sh` (Linux/Mac)
2. 等待系统启动完成
3. 打开浏览器访问 http://localhost:3000
4. 开始您的量化交易之旅！

---

**祝您使用愉快！** 🎯📈
