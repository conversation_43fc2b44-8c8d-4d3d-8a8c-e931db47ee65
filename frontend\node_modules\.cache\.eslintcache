[{"C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\index.tsx": "1", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\App.tsx": "2", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\pages\\Dashboard.tsx": "3", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\pages\\AIAnalysis.tsx": "4", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\pages\\Monitoring.tsx": "5", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\pages\\StockManagement.tsx": "6", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\components\\Layout\\Header.tsx": "7", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\components\\Layout\\Sidebar.tsx": "8", "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\pages\\Settings.tsx": "9"}, {"size": 414, "mtime": 1753922769690, "results": "10", "hashOfConfig": "11"}, {"size": 1252, "mtime": 1753929470286, "results": "12", "hashOfConfig": "11"}, {"size": 7379, "mtime": 1753923246550, "results": "13", "hashOfConfig": "11"}, {"size": 11804, "mtime": 1753922955021, "results": "14", "hashOfConfig": "11"}, {"size": 10638, "mtime": 1753923006781, "results": "15", "hashOfConfig": "11"}, {"size": 8651, "mtime": 1753873948266, "results": "16", "hashOfConfig": "11"}, {"size": 1426, "mtime": 1753873864057, "results": "17", "hashOfConfig": "11"}, {"size": 1458, "mtime": 1753873851232, "results": "18", "hashOfConfig": "11"}, {"size": 11085, "mtime": 1753929352263, "results": "19", "hashOfConfig": "11"}, {"filePath": "20", "messages": "21", "suppressedMessages": "22", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "84tp5k", {"filePath": "23", "messages": "24", "suppressedMessages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "suppressedMessages": "28", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "29", "messages": "30", "suppressedMessages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "32", "messages": "33", "suppressedMessages": "34", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "35", "messages": "36", "suppressedMessages": "37", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\index.tsx", [], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\App.tsx", [], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\pages\\Dashboard.tsx", ["47"], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\pages\\AIAnalysis.tsx", ["48"], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\pages\\Monitoring.tsx", [], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\pages\\StockManagement.tsx", [], [], "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\components\\Layout\\Header.tsx", [], [], "C:\\Owen_Zhang\\量化交易系统\\frontend\\src\\components\\Layout\\Sidebar.tsx", [], [], "C:\\<PERSON>_Zhang\\量化交易系统\\frontend\\src\\pages\\Settings.tsx", ["49"], [], {"ruleId": "50", "severity": 1, "message": "51", "line": 105, "column": 9, "nodeType": "52", "messageId": "53", "endLine": 105, "endColumn": 21}, {"ruleId": "50", "severity": 1, "message": "54", "line": 10, "column": 3, "nodeType": "52", "messageId": "53", "endLine": 10, "endColumn": 8}, {"ruleId": "55", "severity": 1, "message": "56", "line": 68, "column": 6, "nodeType": "57", "endLine": 68, "endColumn": 8, "suggestions": "58"}, "@typescript-eslint/no-unused-vars", "'alertColumns' is assigned a value but never used.", "Identifier", "unusedVar", "'Alert' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'loadConfiguration'. Either include it or remove the dependency array.", "ArrayExpression", ["59"], {"desc": "60", "fix": "61"}, "Update the dependencies array to be: [loadConfiguration]", {"range": "62", "text": "63"}, [1361, 1363], "[loadConfiguration]"]