<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化交易监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header .version {
            opacity: 0.8;
            font-size: 0.9em;
        }

        .main-content {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .card-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 30px;
            max-width: 600px;
            width: 100%;
        }

        .card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            transition: all 0.3s ease;
            cursor: pointer;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card:hover {
            transform: translateY(-5px);
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .card-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }

        .card h3 {
            font-size: 1.3em;
            margin-bottom: 10px;
        }

        .card p {
            opacity: 0.8;
            margin-bottom: 20px;
            line-height: 1.4;
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            background: #40a9ff;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            padding: 20px 0;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #ff4d4f;
            animation: pulse 2s infinite;
        }

        .status-dot.connected {
            background: #52c41a;
        }

        .status-dot.warning {
            background: #faad14;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .info-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
            font-size: 0.9em;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 3px solid #1890ff;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <h1>🚀 量化交易监控系统</h1>
            <div class="version" id="app-version">v1.0.0</div>
        </header>

        <main class="main-content">
            <div class="card-grid">
                <div class="card" id="config-card">
                    <div class="card-icon">🔧</div>
                    <h3>系统配置</h3>
                    <p>配置数据源Token和API密钥</p>
                    <button class="btn" id="config-btn">配置系统</button>
                </div>

                <div class="card" id="start-card">
                    <div class="card-icon">📊</div>
                    <h3>启动系统</h3>
                    <p>启动量化交易监控系统</p>
                    <button class="btn" id="start-btn" disabled>启动系统</button>
                </div>

                <div class="card" id="status-card">
                    <div class="card-icon">📈</div>
                    <h3>系统状态</h3>
                    <p>查看服务运行状态和日志</p>
                    <button class="btn" id="status-btn">查看状态</button>
                </div>

                <div class="card" id="help-card">
                    <div class="card-icon">❓</div>
                    <h3>帮助支持</h3>
                    <p>使用说明和技术支持</p>
                    <button class="btn" id="help-btn">获取帮助</button>
                </div>
            </div>
        </main>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div id="loading-text">正在加载...</div>
        </div>

        <div class="info-panel" id="info-panel" style="display: none;">
            <div id="info-content"></div>
        </div>

        <footer class="footer">
            <div class="status-indicator">
                <span class="status-dot" id="system-status"></span>
                <span id="status-text">系统未配置</span>
            </div>
            <div id="app-info">
                <span id="platform-info"></span>
            </div>
        </footer>
    </div>

    <script>
        // 应用状态
        let appState = {
            config: null,
            serviceStatus: null,
            isConfigured: false
        };

        // DOM元素
        const elements = {
            configBtn: document.getElementById('config-btn'),
            startBtn: document.getElementById('start-btn'),
            statusBtn: document.getElementById('status-btn'),
            helpBtn: document.getElementById('help-btn'),
            systemStatus: document.getElementById('system-status'),
            statusText: document.getElementById('status-text'),
            appVersion: document.getElementById('app-version'),
            platformInfo: document.getElementById('platform-info'),
            loading: document.getElementById('loading'),
            loadingText: document.getElementById('loading-text'),
            infoPanel: document.getElementById('info-panel'),
            infoContent: document.getElementById('info-content')
        };

        // 初始化应用
        async function initializeApp() {
            try {
                showLoading('正在初始化应用...');

                // 获取应用信息
                const appInfo = await window.electronAPI.getAppInfo();
                elements.appVersion.textContent = `v${appInfo.version}`;
                elements.platformInfo.textContent = `${window.platform.os} ${window.platform.arch}`;

                // 加载配置
                await loadConfig();

                // 获取服务状态
                await updateServiceStatus();

                // 设置事件监听
                setupEventListeners();

                hideLoading();
                showInfo('应用初始化完成', 'success');

            } catch (error) {
                console.error('应用初始化失败:', error);
                hideLoading();
                showInfo(`初始化失败: ${error.message}`, 'error');
            }
        }

        // 加载配置
        async function loadConfig() {
            try {
                appState.config = await window.electronAPI.loadConfig();
                updateConfigStatus();
            } catch (error) {
                console.error('加载配置失败:', error);
                throw error;
            }
        }

        // 更新配置状态
        function updateConfigStatus() {
            const hasValidToken = appState.config.tushare.token && 
                                 appState.config.tushare.token.length > 10;
            
            appState.isConfigured = hasValidToken;
            
            if (appState.isConfigured) {
                elements.startBtn.disabled = false;
                updateSystemStatus('configured', '系统已配置');
            } else {
                elements.startBtn.disabled = true;
                updateSystemStatus('unconfigured', '系统未配置');
            }
        }

        // 更新服务状态
        async function updateServiceStatus() {
            try {
                appState.serviceStatus = await window.electronAPI.getServiceStatus();
                
                const backendRunning = appState.serviceStatus.backend.status === 'running';
                const frontendRunning = appState.serviceStatus.frontend.status === 'running';
                
                if (backendRunning && frontendRunning) {
                    updateSystemStatus('running', '系统运行中');
                } else if (backendRunning || frontendRunning) {
                    updateSystemStatus('warning', '部分服务运行中');
                } else {
                    updateSystemStatus('stopped', '系统已停止');
                }
            } catch (error) {
                console.error('获取服务状态失败:', error);
                updateSystemStatus('error', '状态获取失败');
            }
        }

        // 更新系统状态显示
        function updateSystemStatus(status, text) {
            elements.systemStatus.className = `status-dot ${status}`;
            elements.statusText.textContent = text;
        }

        // 设置事件监听
        function setupEventListeners() {
            elements.configBtn.addEventListener('click', () => {
                window.electronAPI.navigateTo('config');
            });

            elements.startBtn.addEventListener('click', async () => {
                if (!appState.isConfigured) {
                    showInfo('请先配置系统', 'warning');
                    return;
                }

                try {
                    showLoading('正在启动服务...');
                    const result = await window.electronAPI.startServices();

                    if (result.success) {
                        showInfo('服务启动成功', 'success');
                        await updateServiceStatus();

                        // 启动成功后，延迟打开主界面
                        setTimeout(() => {
                            window.electronAPI.openExternal('http://localhost:3001');
                        }, 3000);
                    } else {
                        showInfo(`服务启动失败: ${result.error}`, 'error');
                    }
                } catch (error) {
                    showInfo(`启动失败: ${error.message}`, 'error');
                } finally {
                    hideLoading();
                }
            });

            elements.statusBtn.addEventListener('click', () => {
                window.electronAPI.navigateTo('status');
            });

            elements.helpBtn.addEventListener('click', () => {
                window.electronAPI.navigateTo('help');
            });
        }

        // 显示加载状态
        function showLoading(text) {
            elements.loadingText.textContent = text;
            elements.loading.style.display = 'block';
        }

        // 隐藏加载状态
        function hideLoading() {
            elements.loading.style.display = 'none';
        }

        // 显示信息
        function showInfo(message, type = 'info') {
            const colors = {
                success: '#52c41a',
                error: '#ff4d4f',
                warning: '#faad14',
                info: '#1890ff'
            };

            elements.infoContent.innerHTML = `
                <div style="color: ${colors[type]}; font-weight: bold;">
                    ${message}
                </div>
            `;
            elements.infoPanel.style.display = 'block';

            // 3秒后自动隐藏
            setTimeout(() => {
                elements.infoPanel.style.display = 'none';
            }, 3000);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializeApp);

        // 监听首次运行欢迎
        window.electronAPI.onFirstRunWelcome(() => {
            showInfo('欢迎使用量化交易监控系统！请先配置数据源。', 'info');
        });
    </script>
</body>
</html>
