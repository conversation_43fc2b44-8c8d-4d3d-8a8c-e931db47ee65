# ⚡ 快速Token配置指南

## 🚀 最简单的配置方式

### 方法一：使用配置工具（推荐）

1. **双击运行**：`update_tokens.bat`
2. **选择操作**：输入 `1` 更新Tushare Token
3. **输入Token**：粘贴您的Tushare Token
4. **选择操作**：输入 `2` 更新DeepSeek API Key（可选）
5. **输入API Key**：粘贴您的DeepSeek API Key
6. **退出工具**：输入 `5` 退出

### 方法二：Web界面配置

1. **访问设置页面**：http://localhost:3001/settings
2. **输入Token**：在相应字段输入Token
3. **测试连接**：点击"测试连接"验证
4. **保存配置**：点击"保存配置"

### 方法三：直接编辑配置文件

1. **打开文件**：`backend/config/user_config.json`
2. **编辑内容**：
```json
{
  "tushare_token": "您的Tushare_Token",
  "deepseek_api_key": "您的DeepSeek_API_Key",
  "enabled_features": {
    "ai_analysis": true,
    "realtime_data": true,
    "notifications": true
  }
}
```
3. **保存文件**

## 🔑 Token获取

### Tushare Token（必需）
- **网站**：https://tushare.pro/
- **步骤**：注册 → 实名认证 → 获取Token
- **格式**：32位以上字符串

### DeepSeek API Key（可选）
- **网站**：https://platform.deepseek.com/
- **步骤**：注册 → 创建API Key
- **格式**：以`sk-`开头的字符串

## ✅ 验证配置

配置完成后，重启系统验证：

```bash
# 停止系统
Ctrl+C (在运行的终端中)

# 重新启动
start_system.bat
```

## 🆘 遇到问题？

1. **Token格式错误**：检查Token是否完整复制
2. **配置不生效**：重启系统后再试
3. **文件权限问题**：确保有写入权限
4. **网络连接问题**：检查网络连接

## 📞 获取帮助

- 查看详细文档：`TOKEN_CONFIGURATION_GUIDE.md`
- 运行配置工具：`update_tokens.bat`
- 访问Web界面：http://localhost:3001/settings

---

**提示**：建议先配置Tushare Token，这是系统正常运行的必需配置。DeepSeek API Key是可选的，用于AI分析功能。
