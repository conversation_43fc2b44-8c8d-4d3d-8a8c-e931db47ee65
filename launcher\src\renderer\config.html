<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统配置 - 量化交易监控系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            overflow-x: hidden;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .back-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 10px 20px;
            border-radius: 8px;
            cursor: pointer;
            margin-right: 20px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(-2px);
        }

        .header h1 {
            font-size: 2em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .main-content {
            flex: 1;
        }

        .config-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .config-section h2 {
            font-size: 1.3em;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .form-control {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px 15px;
            color: white;
            font-size: 14px;
            transition: all 0.3s ease;
            flex: 1;
        }

        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .form-control:focus {
            outline: none;
            border-color: #1890ff;
            background: rgba(255, 255, 255, 0.15);
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        .btn {
            background: #1890ff;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            min-width: 80px;
        }

        .btn:hover {
            background: #40a9ff;
            transform: translateY(-1px);
        }

        .btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .btn-test {
            background: #52c41a;
            min-width: 60px;
        }

        .btn-test:hover {
            background: #73d13d;
        }

        .btn-test:disabled {
            background: #666;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-top: 10px;
            font-size: 14px;
        }

        .status-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            background: #ff4d4f;
        }

        .status-dot.valid {
            background: #52c41a;
        }

        .status-dot.testing {
            background: #faad14;
            animation: pulse 1.5s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-bottom: 15px;
        }

        .checkbox-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            accent-color: #1890ff;
        }

        .optional-config {
            margin-left: 20px;
            padding-left: 20px;
            border-left: 2px solid rgba(255, 255, 255, 0.2);
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }

        .optional-config.enabled {
            opacity: 1;
        }

        .footer {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid rgba(255, 255, 255, 0.2);
        }

        .btn-secondary {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .btn-primary {
            background: #1890ff;
        }

        .help-text {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            margin-top: 5px;
            line-height: 1.4;
        }

        .loading {
            display: none;
            text-align: center;
            margin: 20px 0;
        }

        .spinner {
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            border-top: 2px solid #1890ff;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            display: none;
        }

        .alert.success {
            background: rgba(82, 196, 26, 0.2);
            border: 1px solid rgba(82, 196, 26, 0.5);
            color: #b7eb8f;
        }

        .alert.error {
            background: rgba(255, 77, 79, 0.2);
            border: 1px solid rgba(255, 77, 79, 0.5);
            color: #ffccc7;
        }

        .alert.warning {
            background: rgba(250, 173, 20, 0.2);
            border: 1px solid rgba(250, 173, 20, 0.5);
            color: #ffe58f;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <button class="back-btn" id="back-btn">← 返回</button>
            <h1>⚙️ 系统配置</h1>
        </header>

        <div class="alert" id="alert">
            <div id="alert-content"></div>
        </div>

        <main class="main-content">
            <form id="config-form">
                <!-- Tushare配置 -->
                <div class="config-section">
                    <h2>📈 Tushare配置 (必需)</h2>
                    <div class="form-group">
                        <label for="tushare-token">Token:</label>
                        <div class="input-group">
                            <input type="text" 
                                   id="tushare-token" 
                                   class="form-control"
                                   placeholder="请输入Tushare Token (32位以上字符串)">
                            <button type="button" class="btn btn-test" id="test-tushare">测试</button>
                        </div>
                        <div class="status-indicator">
                            <span class="status-dot" id="tushare-status"></span>
                            <span id="tushare-status-text">未配置</span>
                        </div>
                        <div class="help-text">
                            获取Token: 访问 <a href="#" id="tushare-link" style="color: #40a9ff;">https://tushare.pro/</a> 注册并获取API Token
                        </div>
                    </div>
                </div>

                <!-- Wind配置 -->
                <div class="config-section">
                    <h2>🏢 万得Wind配置 (可选)</h2>
                    <div class="checkbox-group">
                        <input type="checkbox" id="wind-enabled">
                        <label for="wind-enabled">启用万得数据源</label>
                    </div>
                    <div class="optional-config" id="wind-config">
                        <div class="form-group">
                            <label for="wind-username">用户名:</label>
                            <input type="text" 
                                   id="wind-username" 
                                   class="form-control"
                                   placeholder="Wind用户名">
                        </div>
                        <div class="form-group">
                            <label for="wind-password">密码:</label>
                            <input type="password" 
                                   id="wind-password" 
                                   class="form-control"
                                   placeholder="Wind密码">
                        </div>
                        <div class="form-group">
                            <button type="button" class="btn btn-test" id="test-wind">测试连接</button>
                            <div class="status-indicator">
                                <span class="status-dot" id="wind-status"></span>
                                <span id="wind-status-text">未配置</span>
                            </div>
                        </div>
                        <div class="help-text">
                            需要安装Wind终端软件和WindPy库。年费数万元，适合机构用户。
                        </div>
                    </div>
                </div>

                <!-- 米筐配置 -->
                <div class="config-section">
                    <h2>🌾 米筐配置 (可选)</h2>
                    <div class="checkbox-group">
                        <input type="checkbox" id="ricequant-enabled">
                        <label for="ricequant-enabled">启用米筐数据源</label>
                    </div>
                    <div class="optional-config" id="ricequant-config">
                        <div class="form-group">
                            <label for="ricequant-key">API Key:</label>
                            <div class="input-group">
                                <input type="text" 
                                       id="ricequant-key" 
                                       class="form-control"
                                       placeholder="米筐API Key">
                                <button type="button" class="btn btn-test" id="test-ricequant">测试</button>
                            </div>
                            <div class="status-indicator">
                                <span class="status-dot" id="ricequant-status"></span>
                                <span id="ricequant-status-text">未配置</span>
                            </div>
                        </div>
                        <div class="help-text">
                            获取API Key: 访问 <a href="#" id="ricequant-link" style="color: #40a9ff;">https://www.ricequant.com/</a> 注册并获取API密钥
                        </div>
                    </div>
                </div>

                <!-- AI配置 -->
                <div class="config-section">
                    <h2>🤖 AI分析配置</h2>
                    <div class="form-group">
                        <label for="deepseek-key">DeepSeek API Key:</label>
                        <input type="text" 
                               id="deepseek-key" 
                               class="form-control"
                               placeholder="DeepSeek API Key">
                        <div class="help-text">
                            已预配置默认Key，可选择更换。获取Key: <a href="#" id="deepseek-link" style="color: #40a9ff;">https://platform.deepseek.com/</a>
                        </div>
                    </div>
                </div>
            </form>
        </main>

        <div class="loading" id="loading">
            <div class="spinner"></div>
            <div id="loading-text">正在处理...</div>
        </div>

        <footer class="footer">
            <button type="button" class="btn btn-secondary" id="reset-btn">🔄 重置配置</button>
            <div>
                <button type="button" class="btn btn-secondary" id="cancel-btn">❌ 取消</button>
                <button type="button" class="btn btn-primary" id="save-btn" style="margin-left: 10px;">💾 保存配置</button>
            </div>
        </footer>
    </div>

    <script src="js/config.js"></script>
</body>
</html>
