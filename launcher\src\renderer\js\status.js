// 状态页面逻辑
class StatusPage {
    constructor() {
        this.config = null;
        this.serviceStatus = null;
        this.healthStatus = null;
        this.refreshInterval = null;
        this.isLoading = false;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadInitialData();
        this.startAutoRefresh();
    }

    initializeElements() {
        this.elements = {
            // 基础元素
            backBtn: document.getElementById('back-btn'),
            refreshBtn: document.getElementById('refresh-btn'),
            loading: document.getElementById('loading'),
            loadingText: document.getElementById('loading-text'),

            // 整体健康状态
            healthDot: document.getElementById('health-dot'),
            healthText: document.getElementById('health-text'),

            // 服务状态
            backendStatus: document.getElementById('backend-status'),
            backendStatusText: document.getElementById('backend-status-text'),
            backendDetails: document.getElementById('backend-details'),
            frontendStatus: document.getElementById('frontend-status'),
            frontendStatusText: document.getElementById('frontend-status-text'),
            frontendDetails: document.getElementById('frontend-details'),

            // 操作按钮
            startBtn: document.getElementById('start-btn'),
            stopBtn: document.getElementById('stop-btn'),
            restartBtn: document.getElementById('restart-btn'),

            // URL区域
            urlSection: document.getElementById('url-section'),
            frontendUrl: document.getElementById('frontend-url'),
            backendUrl: document.getElementById('backend-url'),

            // 数据源状态
            tushareStatus: document.getElementById('tushare-status'),
            tushareStatusText: document.getElementById('tushare-status-text'),
            windStatus: document.getElementById('wind-status'),
            windStatusText: document.getElementById('wind-status-text'),
            ricequantStatus: document.getElementById('ricequant-status'),
            ricequantStatusText: document.getElementById('ricequant-status-text'),
            deepseekStatus: document.getElementById('deepseek-status'),
            deepseekStatusText: document.getElementById('deepseek-status-text'),

            // 日志
            logContainer: document.getElementById('log-container')
        };
    }

    setupEventListeners() {
        // 导航按钮
        this.elements.backBtn.addEventListener('click', () => this.goBack());
        this.elements.refreshBtn.addEventListener('click', () => this.refreshStatus());

        // 服务控制按钮
        this.elements.startBtn.addEventListener('click', () => this.startServices());
        this.elements.stopBtn.addEventListener('click', () => this.stopServices());
        this.elements.restartBtn.addEventListener('click', () => this.restartServices());

        // URL链接
        this.elements.frontendUrl.addEventListener('click', (e) => {
            e.preventDefault();
            if (this.serviceStatus?.frontend?.status === 'running') {
                window.electronAPI.openExternal(this.serviceStatus.frontend.url);
            }
        });

        this.elements.backendUrl.addEventListener('click', (e) => {
            e.preventDefault();
            if (this.serviceStatus?.backend?.status === 'running') {
                window.electronAPI.openExternal(`${this.serviceStatus.backend.url}/docs`);
            }
        });

        // 监听服务状态变化
        window.electronAPI.onServiceStatusChange((status) => {
            this.serviceStatus = status;
            this.updateServiceStatus();
        });

        // 监听健康检查结果
        window.electronAPI.onHealthCheck((health) => {
            this.healthStatus = health;
            this.updateHealthStatus();
        });
    }

    async loadInitialData() {
        try {
            this.showLoading('正在加载状态信息...');

            // 加载配置
            this.config = await window.electronAPI.loadConfig();
            this.updateDataSourceStatus();

            // 获取服务状态
            await this.refreshStatus();

            // 启动健康监控
            await window.electronAPI.startHealthMonitoring();

            this.hideLoading();
            this.addLogEntry('状态页面初始化完成', 'info');

        } catch (error) {
            this.hideLoading();
            this.addLogEntry(`初始化失败: ${error.message}`, 'error');
            console.error('状态页面初始化失败:', error);
        }
    }

    async refreshStatus() {
        try {
            if (this.isLoading) return;

            this.showLoading('正在刷新状态...');

            // 获取服务状态
            this.serviceStatus = await window.electronAPI.getServiceStatus();
            this.updateServiceStatus();

            // 获取日志
            const logs = await window.electronAPI.getLogs();
            this.updateLogs(logs);

            this.hideLoading();
            this.addLogEntry('状态刷新完成', 'info');

        } catch (error) {
            this.hideLoading();
            this.addLogEntry(`刷新失败: ${error.message}`, 'error');
            console.error('刷新状态失败:', error);
        }
    }

    updateServiceStatus() {
        if (!this.serviceStatus) return;

        // 更新后端状态
        this.updateServiceIndicator('backend', this.serviceStatus.backend);
        
        // 更新前端状态
        this.updateServiceIndicator('frontend', this.serviceStatus.frontend);

        // 更新操作按钮状态
        this.updateActionButtons();

        // 更新URL区域
        this.updateUrlSection();

        // 更新整体健康状态
        this.updateOverallHealth();
    }

    updateServiceIndicator(serviceName, serviceInfo) {
        const statusDot = this.elements[`${serviceName}Status`];
        const statusText = this.elements[`${serviceName}StatusText`];
        const details = this.elements[`${serviceName}Details`];

        // 更新状态点
        statusDot.className = 'status-dot';
        switch (serviceInfo.status) {
            case 'running':
                statusDot.classList.add('running');
                statusText.textContent = '运行中';
                break;
            case 'starting':
                statusDot.classList.add('starting');
                statusText.textContent = '启动中';
                break;
            case 'error':
                statusDot.classList.add('error');
                statusText.textContent = '错误';
                break;
            default:
                statusText.textContent = '已停止';
        }

        // 更新详细信息
        const pid = serviceInfo.pid || '-';
        const responseTime = this.healthStatus?.status?.[serviceName]?.responseTime || '-';
        details.textContent = `端口: ${serviceInfo.port} | PID: ${pid} | 响应时间: ${responseTime}ms`;
    }

    updateActionButtons() {
        const backendRunning = this.serviceStatus?.backend?.status === 'running';
        const frontendRunning = this.serviceStatus?.frontend?.status === 'running';
        const anyRunning = backendRunning || frontendRunning;
        const anyStarting = this.serviceStatus?.backend?.status === 'starting' || 
                           this.serviceStatus?.frontend?.status === 'starting';

        this.elements.startBtn.disabled = anyRunning || anyStarting;
        this.elements.stopBtn.disabled = !anyRunning;
        this.elements.restartBtn.disabled = !anyRunning || anyStarting;
    }

    updateUrlSection() {
        const backendRunning = this.serviceStatus?.backend?.status === 'running';
        const frontendRunning = this.serviceStatus?.frontend?.status === 'running';

        if (backendRunning || frontendRunning) {
            this.elements.urlSection.style.display = 'block';
            
            // 更新URL链接状态
            if (frontendRunning) {
                this.elements.frontendUrl.style.color = '#40a9ff';
                this.elements.frontendUrl.style.pointerEvents = 'auto';
            } else {
                this.elements.frontendUrl.style.color = '#666';
                this.elements.frontendUrl.style.pointerEvents = 'none';
            }

            if (backendRunning) {
                this.elements.backendUrl.style.color = '#40a9ff';
                this.elements.backendUrl.style.pointerEvents = 'auto';
            } else {
                this.elements.backendUrl.style.color = '#666';
                this.elements.backendUrl.style.pointerEvents = 'none';
            }
        } else {
            this.elements.urlSection.style.display = 'none';
        }
    }

    updateOverallHealth() {
        const backendHealthy = this.healthStatus?.status?.backend?.healthy;
        const frontendHealthy = this.healthStatus?.status?.frontend?.healthy;
        const databaseHealthy = this.healthStatus?.status?.database?.healthy;

        let healthClass = '';
        let healthText = '';

        if (backendHealthy && frontendHealthy && databaseHealthy) {
            healthClass = 'healthy';
            healthText = '系统健康';
        } else if (backendHealthy && frontendHealthy) {
            healthClass = 'warning';
            healthText = '部分功能正常';
        } else if (backendHealthy || frontendHealthy) {
            healthClass = 'warning';
            healthText = '部分服务运行中';
        } else {
            healthClass = '';
            healthText = '系统未运行';
        }

        this.elements.healthDot.className = `health-dot ${healthClass}`;
        this.elements.healthText.textContent = healthText;
    }

    updateDataSourceStatus() {
        if (!this.config) return;

        // 更新Tushare状态
        this.updateDataSourceIndicator('tushare', this.config.tushare);
        
        // 更新Wind状态
        this.updateDataSourceIndicator('wind', this.config.wind);
        
        // 更新米筐状态
        this.updateDataSourceIndicator('ricequant', this.config.ricequant);
        
        // DeepSeek状态
        const deepseekConfigured = this.config.deepseek?.api_key && 
                                  this.config.deepseek.api_key !== '';
        this.elements.deepseekStatus.className = deepseekConfigured ? 'status-dot running' : 'status-dot';
        this.elements.deepseekStatusText.textContent = deepseekConfigured ? '已配置' : '未配置';
    }

    updateDataSourceIndicator(source, config) {
        const statusDot = this.elements[`${source}Status`];
        const statusText = this.elements[`${source}StatusText`];

        statusDot.className = 'status-dot';

        if (config.enabled && config.status === 'valid') {
            statusDot.classList.add('running');
            statusText.textContent = '已连接';
        } else if (config.enabled) {
            statusText.textContent = '已启用';
        } else {
            statusText.textContent = '未启用';
        }
    }

    updateHealthStatus() {
        if (!this.healthStatus) return;

        this.updateOverallHealth();
        this.updateServiceStatus(); // 重新更新服务状态以包含响应时间
    }

    updateLogs(logs) {
        if (!logs) return;

        // 合并后端和前端日志
        const allLogs = [
            ...logs.backend.map(log => ({ ...log, source: 'backend' })),
            ...logs.frontend.map(log => ({ ...log, source: 'frontend' }))
        ];

        // 按时间排序
        allLogs.sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

        // 清空现有日志
        this.elements.logContainer.innerHTML = '';

        // 显示最近的50条日志
        const recentLogs = allLogs.slice(-50);
        recentLogs.forEach(log => {
            this.addLogEntryToContainer(log.message, log.level.toLowerCase(), log.timestamp, log.source);
        });

        // 滚动到底部
        this.elements.logContainer.scrollTop = this.elements.logContainer.scrollHeight;
    }

    addLogEntry(message, level = 'info', source = 'system') {
        const timestamp = new Date().toISOString();
        this.addLogEntryToContainer(message, level, timestamp, source);
    }

    addLogEntryToContainer(message, level, timestamp, source) {
        const logEntry = document.createElement('div');
        logEntry.className = `log-entry ${level}`;
        
        const time = new Date(timestamp).toLocaleTimeString();
        logEntry.innerHTML = `
            <span class="log-timestamp">[${time}]</span>
            <span>[${source.toUpperCase()}]</span>
            <span>${message}</span>
        `;

        this.elements.logContainer.appendChild(logEntry);

        // 保持日志数量限制
        const logEntries = this.elements.logContainer.children;
        if (logEntries.length > 100) {
            this.elements.logContainer.removeChild(logEntries[0]);
        }

        // 滚动到底部
        this.elements.logContainer.scrollTop = this.elements.logContainer.scrollHeight;
    }

    async startServices() {
        try {
            this.showLoading('正在启动服务...');
            this.addLogEntry('开始启动服务', 'info');

            const result = await window.electronAPI.startServices();
            
            if (result.success) {
                this.addLogEntry('服务启动成功', 'info');
            } else {
                this.addLogEntry(`服务启动失败: ${result.error}`, 'error');
            }

            this.hideLoading();
            await this.refreshStatus();

        } catch (error) {
            this.hideLoading();
            this.addLogEntry(`启动服务失败: ${error.message}`, 'error');
            console.error('启动服务失败:', error);
        }
    }

    async stopServices() {
        try {
            this.showLoading('正在停止服务...');
            this.addLogEntry('开始停止服务', 'info');

            const result = await window.electronAPI.stopServices();
            
            if (result.success) {
                this.addLogEntry('服务停止成功', 'info');
            } else {
                this.addLogEntry(`服务停止失败: ${result.error}`, 'error');
            }

            this.hideLoading();
            await this.refreshStatus();

        } catch (error) {
            this.hideLoading();
            this.addLogEntry(`停止服务失败: ${error.message}`, 'error');
            console.error('停止服务失败:', error);
        }
    }

    async restartServices() {
        try {
            this.showLoading('正在重启服务...');
            this.addLogEntry('开始重启服务', 'info');

            const result = await window.electronAPI.restartServices();
            
            if (result.success) {
                this.addLogEntry('服务重启成功', 'info');
            } else {
                this.addLogEntry(`服务重启失败: ${result.error}`, 'error');
            }

            this.hideLoading();
            await this.refreshStatus();

        } catch (error) {
            this.hideLoading();
            this.addLogEntry(`重启服务失败: ${error.message}`, 'error');
            console.error('重启服务失败:', error);
        }
    }

    startAutoRefresh() {
        // 每30秒自动刷新一次状态
        this.refreshInterval = setInterval(() => {
            if (!this.isLoading) {
                this.refreshStatus();
            }
        }, 30000);
    }

    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    goBack() {
        this.stopAutoRefresh();
        window.electronAPI.stopHealthMonitoring();
        window.electronAPI.navigateTo('index');
    }

    showLoading(text) {
        this.elements.loadingText.textContent = text;
        this.elements.loading.style.display = 'block';
        this.isLoading = true;
    }

    hideLoading() {
        this.elements.loading.style.display = 'none';
        this.isLoading = false;
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    new StatusPage();
});

// 页面卸载时清理
window.addEventListener('beforeunload', () => {
    if (window.statusPage) {
        window.statusPage.stopAutoRefresh();
    }
});
