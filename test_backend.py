#!/usr/bin/env python3
"""
简化的后端测试启动脚本
"""
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

def test_imports():
    """测试关键模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        import fastapi
        print("✅ FastAPI导入成功")
    except ImportError as e:
        print(f"❌ FastAPI导入失败: {e}")
        return False
    
    try:
        import uvicorn
        print("✅ Uvicorn导入成功")
    except ImportError as e:
        print(f"❌ Uvicorn导入失败: {e}")
        return False
    
    try:
        import pandas
        print("✅ Pandas导入成功")
    except ImportError as e:
        print(f"❌ Pandas导入失败: {e}")
        return False
    
    try:
        import numpy
        print("✅ Numpy导入成功")
    except ImportError as e:
        print(f"❌ Numpy导入失败: {e}")
        return False
    
    return True

def test_app_import():
    """测试应用导入"""
    print("\n🧪 测试应用导入...")
    
    try:
        # 切换到backend目录
        os.chdir('backend')
        
        # 测试配置导入
        from app.core.config import Settings
        settings = Settings()
        print("✅ 配置模块导入成功")
        
        # 测试主应用导入
        from app.main import app
        print("✅ 主应用导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 应用导入失败: {e}")
        return False

def start_server():
    """启动服务器"""
    print("\n🚀 启动后端服务器...")
    
    try:
        import uvicorn
        from app.main import app
        
        print("📊 服务器信息:")
        print("  - 地址: http://localhost:8000")
        print("  - API文档: http://localhost:8000/docs")
        print("  - 健康检查: http://localhost:8000/health")
        print("\n按 Ctrl+C 停止服务器")
        
        uvicorn.run(
            app, 
            host="0.0.0.0", 
            port=8000,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

def main():
    """主函数"""
    print("=" * 50)
    print("🚀 量化交易系统后端测试")
    print("=" * 50)
    
    # 测试基础依赖
    if not test_imports():
        print("\n❌ 基础依赖测试失败，请检查Python环境")
        return
    
    # 测试应用导入
    if not test_app_import():
        print("\n❌ 应用导入测试失败，请检查代码")
        return
    
    print("\n✅ 所有测试通过！")
    
    # 询问是否启动服务器
    try:
        choice = input("\n是否启动后端服务器？(y/n): ").lower().strip()
        if choice in ['y', 'yes', '是']:
            start_server()
        else:
            print("👋 测试完成")
    except KeyboardInterrupt:
        print("\n👋 测试完成")

if __name__ == "__main__":
    main()
