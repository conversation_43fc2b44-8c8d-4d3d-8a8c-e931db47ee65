{"ast": null, "code": "/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Scale from './Scale.js';\nimport * as numberUtil from '../util/number.js';\nimport * as scaleHelper from './helper.js';\n// Use some method of IntervalScale\nimport IntervalScale from './Interval.js';\nvar scaleProto = Scale.prototype;\n// FIXME:TS refactor: not good to call it directly with `this`?\nvar intervalScaleProto = IntervalScale.prototype;\nvar roundingErrorFix = numberUtil.round;\nvar mathFloor = Math.floor;\nvar mathCeil = Math.ceil;\nvar mathPow = Math.pow;\nvar mathLog = Math.log;\nvar LogScale = /** @class */function (_super) {\n  __extends(LogScale, _super);\n  function LogScale() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = 'log';\n    _this.base = 10;\n    _this._originalScale = new IntervalScale();\n    // FIXME:TS actually used by `IntervalScale`\n    _this._interval = 0;\n    return _this;\n  }\n  /**\r\n   * @param Whether expand the ticks to niced extent.\r\n   */\n  LogScale.prototype.getTicks = function (expandToNicedExtent) {\n    var originalScale = this._originalScale;\n    var extent = this._extent;\n    var originalExtent = originalScale.getExtent();\n    var ticks = intervalScaleProto.getTicks.call(this, expandToNicedExtent);\n    return zrUtil.map(ticks, function (tick) {\n      var val = tick.value;\n      var powVal = numberUtil.round(mathPow(this.base, val));\n      // Fix #4158\n      powVal = val === extent[0] && this._fixMin ? fixRoundingError(powVal, originalExtent[0]) : powVal;\n      powVal = val === extent[1] && this._fixMax ? fixRoundingError(powVal, originalExtent[1]) : powVal;\n      return {\n        value: powVal\n      };\n    }, this);\n  };\n  LogScale.prototype.setExtent = function (start, end) {\n    var base = mathLog(this.base);\n    // log(-Infinity) is NaN, so safe guard here\n    start = mathLog(Math.max(0, start)) / base;\n    end = mathLog(Math.max(0, end)) / base;\n    intervalScaleProto.setExtent.call(this, start, end);\n  };\n  /**\r\n   * @return {number} end\r\n   */\n  LogScale.prototype.getExtent = function () {\n    var base = this.base;\n    var extent = scaleProto.getExtent.call(this);\n    extent[0] = mathPow(base, extent[0]);\n    extent[1] = mathPow(base, extent[1]);\n    // Fix #4158\n    var originalScale = this._originalScale;\n    var originalExtent = originalScale.getExtent();\n    this._fixMin && (extent[0] = fixRoundingError(extent[0], originalExtent[0]));\n    this._fixMax && (extent[1] = fixRoundingError(extent[1], originalExtent[1]));\n    return extent;\n  };\n  LogScale.prototype.unionExtent = function (extent) {\n    this._originalScale.unionExtent(extent);\n    var base = this.base;\n    extent[0] = mathLog(extent[0]) / mathLog(base);\n    extent[1] = mathLog(extent[1]) / mathLog(base);\n    scaleProto.unionExtent.call(this, extent);\n  };\n  LogScale.prototype.unionExtentFromData = function (data, dim) {\n    // TODO\n    // filter value that <= 0\n    this.unionExtent(data.getApproximateExtent(dim));\n  };\n  /**\r\n   * Update interval and extent of intervals for nice ticks\r\n   * @param approxTickNum default 10 Given approx tick number\r\n   */\n  LogScale.prototype.calcNiceTicks = function (approxTickNum) {\n    approxTickNum = approxTickNum || 10;\n    var extent = this._extent;\n    var span = extent[1] - extent[0];\n    if (span === Infinity || span <= 0) {\n      return;\n    }\n    var interval = numberUtil.quantity(span);\n    var err = approxTickNum / span * interval;\n    // Filter ticks to get closer to the desired count.\n    if (err <= 0.5) {\n      interval *= 10;\n    }\n    // Interval should be integer\n    while (!isNaN(interval) && Math.abs(interval) < 1 && Math.abs(interval) > 0) {\n      interval *= 10;\n    }\n    var niceExtent = [numberUtil.round(mathCeil(extent[0] / interval) * interval), numberUtil.round(mathFloor(extent[1] / interval) * interval)];\n    this._interval = interval;\n    this._niceExtent = niceExtent;\n  };\n  LogScale.prototype.calcNiceExtent = function (opt) {\n    intervalScaleProto.calcNiceExtent.call(this, opt);\n    this._fixMin = opt.fixMin;\n    this._fixMax = opt.fixMax;\n  };\n  LogScale.prototype.parse = function (val) {\n    return val;\n  };\n  LogScale.prototype.contain = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return scaleHelper.contain(val, this._extent);\n  };\n  LogScale.prototype.normalize = function (val) {\n    val = mathLog(val) / mathLog(this.base);\n    return scaleHelper.normalize(val, this._extent);\n  };\n  LogScale.prototype.scale = function (val) {\n    val = scaleHelper.scale(val, this._extent);\n    return mathPow(this.base, val);\n  };\n  LogScale.type = 'log';\n  return LogScale;\n}(Scale);\nvar proto = LogScale.prototype;\nproto.getMinorTicks = intervalScaleProto.getMinorTicks;\nproto.getLabel = intervalScaleProto.getLabel;\nfunction fixRoundingError(val, originalVal) {\n  return roundingErrorFix(val, numberUtil.getPrecision(originalVal));\n}\nScale.registerClass(LogScale);\nexport default LogScale;", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}