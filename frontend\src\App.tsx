import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Layout } from 'antd';
import Sidebar from './components/Layout/Sidebar.tsx';
import Header from './components/Layout/Header.tsx';
import Dashboard from './pages/Dashboard.tsx';
import StockManagement from './pages/StockManagement.tsx';
import Monitoring from './pages/Monitoring.tsx';
import AIAnalysis from './pages/AIAnalysis.tsx';
import Settings from './pages/Settings.tsx';

const { Content } = Layout;

const App: React.FC = () => {
  return (
    <Router>
      <Layout style={{ minHeight: '100vh' }}>
        <Sidebar />
        <Layout>
          <Header />
          <Content style={{ margin: '16px' }}>
            <Routes>
              <Route path="/" element={<Dashboard />} />
              <Route path="/stocks" element={<StockManagement />} />
              <Route path="/monitoring" element={<Monitoring />} />
              <Route path="/indicators" element={<Monitoring />} />
              <Route path="/ai" element={<AIAnalysis />} />
              <Route path="/settings" element={<Settings />} />
            </Routes>
          </Content>
        </Layout>
      </Layout>
    </Router>
  );
};

export default App;
