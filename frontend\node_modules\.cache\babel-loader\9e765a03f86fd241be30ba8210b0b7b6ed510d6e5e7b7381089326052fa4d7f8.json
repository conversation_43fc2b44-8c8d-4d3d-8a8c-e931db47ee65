{"ast": null, "code": "import { fromPoints } from '../core/bbox.js';\nimport BoundingRect from '../core/BoundingRect.js';\nimport Point from '../core/Point.js';\nimport { map } from '../core/util.js';\nimport Polygon from '../graphic/shape/Polygon.js';\nimport Rect from '../graphic/shape/Rect.js';\nimport Sector from '../graphic/shape/Sector.js';\nimport { pathToPolygons } from './convertPath.js';\nimport { clonePath } from './path.js';\nfunction getDividingGrids(dimSize, rowDim, count) {\n  var rowSize = dimSize[rowDim];\n  var columnSize = dimSize[1 - rowDim];\n  var ratio = Math.abs(rowSize / columnSize);\n  var rowCount = Math.ceil(Math.sqrt(ratio * count));\n  var columnCount = Math.floor(count / rowCount);\n  if (columnCount === 0) {\n    columnCount = 1;\n    rowCount = count;\n  }\n  var grids = [];\n  for (var i = 0; i < rowCount; i++) {\n    grids.push(columnCount);\n  }\n  var currentCount = rowCount * columnCount;\n  var remained = count - currentCount;\n  if (remained > 0) {\n    for (var i = 0; i < remained; i++) {\n      grids[i % rowCount] += 1;\n    }\n  }\n  return grids;\n}\nfunction divideSector(sectorShape, count, outShapes) {\n  var r0 = sectorShape.r0;\n  var r = sectorShape.r;\n  var startAngle = sectorShape.startAngle;\n  var endAngle = sectorShape.endAngle;\n  var angle = Math.abs(endAngle - startAngle);\n  var arcLen = angle * r;\n  var deltaR = r - r0;\n  var isAngleRow = arcLen > Math.abs(deltaR);\n  var grids = getDividingGrids([arcLen, deltaR], isAngleRow ? 0 : 1, count);\n  var rowSize = (isAngleRow ? angle : deltaR) / grids.length;\n  for (var row = 0; row < grids.length; row++) {\n    var columnSize = (isAngleRow ? deltaR : angle) / grids[row];\n    for (var column = 0; column < grids[row]; column++) {\n      var newShape = {};\n      if (isAngleRow) {\n        newShape.startAngle = startAngle + rowSize * row;\n        newShape.endAngle = startAngle + rowSize * (row + 1);\n        newShape.r0 = r0 + columnSize * column;\n        newShape.r = r0 + columnSize * (column + 1);\n      } else {\n        newShape.startAngle = startAngle + columnSize * column;\n        newShape.endAngle = startAngle + columnSize * (column + 1);\n        newShape.r0 = r0 + rowSize * row;\n        newShape.r = r0 + rowSize * (row + 1);\n      }\n      newShape.clockwise = sectorShape.clockwise;\n      newShape.cx = sectorShape.cx;\n      newShape.cy = sectorShape.cy;\n      outShapes.push(newShape);\n    }\n  }\n}\nfunction divideRect(rectShape, count, outShapes) {\n  var width = rectShape.width;\n  var height = rectShape.height;\n  var isHorizontalRow = width > height;\n  var grids = getDividingGrids([width, height], isHorizontalRow ? 0 : 1, count);\n  var rowSizeDim = isHorizontalRow ? 'width' : 'height';\n  var columnSizeDim = isHorizontalRow ? 'height' : 'width';\n  var rowDim = isHorizontalRow ? 'x' : 'y';\n  var columnDim = isHorizontalRow ? 'y' : 'x';\n  var rowSize = rectShape[rowSizeDim] / grids.length;\n  for (var row = 0; row < grids.length; row++) {\n    var columnSize = rectShape[columnSizeDim] / grids[row];\n    for (var column = 0; column < grids[row]; column++) {\n      var newShape = {};\n      newShape[rowDim] = row * rowSize;\n      newShape[columnDim] = column * columnSize;\n      newShape[rowSizeDim] = rowSize;\n      newShape[columnSizeDim] = columnSize;\n      newShape.x += rectShape.x;\n      newShape.y += rectShape.y;\n      outShapes.push(newShape);\n    }\n  }\n}\nfunction crossProduct2d(x1, y1, x2, y2) {\n  return x1 * y2 - x2 * y1;\n}\nfunction lineLineIntersect(a1x, a1y, a2x, a2y, b1x, b1y, b2x, b2y) {\n  var mx = a2x - a1x;\n  var my = a2y - a1y;\n  var nx = b2x - b1x;\n  var ny = b2y - b1y;\n  var nmCrossProduct = crossProduct2d(nx, ny, mx, my);\n  if (Math.abs(nmCrossProduct) < 1e-6) {\n    return null;\n  }\n  var b1a1x = a1x - b1x;\n  var b1a1y = a1y - b1y;\n  var p = crossProduct2d(b1a1x, b1a1y, nx, ny) / nmCrossProduct;\n  if (p < 0 || p > 1) {\n    return null;\n  }\n  return new Point(p * mx + a1x, p * my + a1y);\n}\nfunction projPtOnLine(pt, lineA, lineB) {\n  var dir = new Point();\n  Point.sub(dir, lineB, lineA);\n  dir.normalize();\n  var dir2 = new Point();\n  Point.sub(dir2, pt, lineA);\n  var len = dir2.dot(dir);\n  return len;\n}\nfunction addToPoly(poly, pt) {\n  var last = poly[poly.length - 1];\n  if (last && last[0] === pt[0] && last[1] === pt[1]) {\n    return;\n  }\n  poly.push(pt);\n}\nfunction splitPolygonByLine(points, lineA, lineB) {\n  var len = points.length;\n  var intersections = [];\n  for (var i = 0; i < len; i++) {\n    var p0 = points[i];\n    var p1 = points[(i + 1) % len];\n    var intersectionPt = lineLineIntersect(p0[0], p0[1], p1[0], p1[1], lineA.x, lineA.y, lineB.x, lineB.y);\n    if (intersectionPt) {\n      intersections.push({\n        projPt: projPtOnLine(intersectionPt, lineA, lineB),\n        pt: intersectionPt,\n        idx: i\n      });\n    }\n  }\n  if (intersections.length < 2) {\n    return [{\n      points: points\n    }, {\n      points: points\n    }];\n  }\n  intersections.sort(function (a, b) {\n    return a.projPt - b.projPt;\n  });\n  var splitPt0 = intersections[0];\n  var splitPt1 = intersections[intersections.length - 1];\n  if (splitPt1.idx < splitPt0.idx) {\n    var tmp = splitPt0;\n    splitPt0 = splitPt1;\n    splitPt1 = tmp;\n  }\n  var splitPt0Arr = [splitPt0.pt.x, splitPt0.pt.y];\n  var splitPt1Arr = [splitPt1.pt.x, splitPt1.pt.y];\n  var newPolyA = [splitPt0Arr];\n  var newPolyB = [splitPt1Arr];\n  for (var i = splitPt0.idx + 1; i <= splitPt1.idx; i++) {\n    addToPoly(newPolyA, points[i].slice());\n  }\n  addToPoly(newPolyA, splitPt1Arr);\n  addToPoly(newPolyA, splitPt0Arr);\n  for (var i = splitPt1.idx + 1; i <= splitPt0.idx + len; i++) {\n    addToPoly(newPolyB, points[i % len].slice());\n  }\n  addToPoly(newPolyB, splitPt0Arr);\n  addToPoly(newPolyB, splitPt1Arr);\n  return [{\n    points: newPolyA\n  }, {\n    points: newPolyB\n  }];\n}\nfunction binaryDividePolygon(polygonShape) {\n  var points = polygonShape.points;\n  var min = [];\n  var max = [];\n  fromPoints(points, min, max);\n  var boundingRect = new BoundingRect(min[0], min[1], max[0] - min[0], max[1] - min[1]);\n  var width = boundingRect.width;\n  var height = boundingRect.height;\n  var x = boundingRect.x;\n  var y = boundingRect.y;\n  var pt0 = new Point();\n  var pt1 = new Point();\n  if (width > height) {\n    pt0.x = pt1.x = x + width / 2;\n    pt0.y = y;\n    pt1.y = y + height;\n  } else {\n    pt0.y = pt1.y = y + height / 2;\n    pt0.x = x;\n    pt1.x = x + width;\n  }\n  return splitPolygonByLine(points, pt0, pt1);\n}\nfunction binaryDivideRecursive(divider, shape, count, out) {\n  if (count === 1) {\n    out.push(shape);\n  } else {\n    var mid = Math.floor(count / 2);\n    var sub = divider(shape);\n    binaryDivideRecursive(divider, sub[0], mid, out);\n    binaryDivideRecursive(divider, sub[1], count - mid, out);\n  }\n  return out;\n}\nexport function clone(path, count) {\n  var paths = [];\n  for (var i = 0; i < count; i++) {\n    paths.push(clonePath(path));\n  }\n  return paths;\n}\nfunction copyPathProps(source, target) {\n  target.setStyle(source.style);\n  target.z = source.z;\n  target.z2 = source.z2;\n  target.zlevel = source.zlevel;\n}\nfunction polygonConvert(points) {\n  var out = [];\n  for (var i = 0; i < points.length;) {\n    out.push([points[i++], points[i++]]);\n  }\n  return out;\n}\nexport function split(path, count) {\n  var outShapes = [];\n  var shape = path.shape;\n  var OutShapeCtor;\n  switch (path.type) {\n    case 'rect':\n      divideRect(shape, count, outShapes);\n      OutShapeCtor = Rect;\n      break;\n    case 'sector':\n      divideSector(shape, count, outShapes);\n      OutShapeCtor = Sector;\n      break;\n    case 'circle':\n      divideSector({\n        r0: 0,\n        r: shape.r,\n        startAngle: 0,\n        endAngle: Math.PI * 2,\n        cx: shape.cx,\n        cy: shape.cy\n      }, count, outShapes);\n      OutShapeCtor = Sector;\n      break;\n    default:\n      var m = path.getComputedTransform();\n      var scale = m ? Math.sqrt(Math.max(m[0] * m[0] + m[1] * m[1], m[2] * m[2] + m[3] * m[3])) : 1;\n      var polygons = map(pathToPolygons(path.getUpdatedPathProxy(), scale), function (poly) {\n        return polygonConvert(poly);\n      });\n      var polygonCount = polygons.length;\n      if (polygonCount === 0) {\n        binaryDivideRecursive(binaryDividePolygon, {\n          points: polygons[0]\n        }, count, outShapes);\n      } else if (polygonCount === count) {\n        for (var i = 0; i < polygonCount; i++) {\n          outShapes.push({\n            points: polygons[i]\n          });\n        }\n      } else {\n        var totalArea_1 = 0;\n        var items = map(polygons, function (poly) {\n          var min = [];\n          var max = [];\n          fromPoints(poly, min, max);\n          var area = (max[1] - min[1]) * (max[0] - min[0]);\n          totalArea_1 += area;\n          return {\n            poly: poly,\n            area: area\n          };\n        });\n        items.sort(function (a, b) {\n          return b.area - a.area;\n        });\n        var left = count;\n        for (var i = 0; i < polygonCount; i++) {\n          var item = items[i];\n          if (left <= 0) {\n            break;\n          }\n          var selfCount = i === polygonCount - 1 ? left : Math.ceil(item.area / totalArea_1 * count);\n          if (selfCount < 0) {\n            continue;\n          }\n          binaryDivideRecursive(binaryDividePolygon, {\n            points: item.poly\n          }, selfCount, outShapes);\n          left -= selfCount;\n        }\n        ;\n      }\n      OutShapeCtor = Polygon;\n      break;\n  }\n  if (!OutShapeCtor) {\n    return clone(path, count);\n  }\n  var out = [];\n  for (var i = 0; i < outShapes.length; i++) {\n    var subPath = new OutShapeCtor();\n    subPath.setShape(outShapes[i]);\n    copyPathProps(path, subPath);\n    out.push(subPath);\n  }\n  return out;\n}", "map": null, "metadata": {}, "sourceType": "module", "externalDependencies": []}