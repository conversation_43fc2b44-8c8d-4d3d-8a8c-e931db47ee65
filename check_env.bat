@echo off
echo ========================================
echo Environment Check
echo ========================================
echo.

echo Checking Python...
python --version
if %errorlevel% neq 0 (
    echo ERROR: Python not found
    goto :end
)

echo.
echo Checking Node.js...
node --version
if %errorlevel% neq 0 (
    echo ERROR: Node.js not found
    goto :end
)

echo.
echo Checking npm...
npm --version
if %errorlevel% neq 0 (
    echo ERROR: npm not found
    goto :end
)

echo.
echo Checking current directory...
echo Current directory: %CD%
dir /b

echo.
echo Checking backend directory...
if exist "backend" (
    echo OK: backend directory exists
    cd backend
    if exist "app" (
        echo OK: app directory exists
    ) else (
        echo ERROR: app directory not found
    )
    cd ..
) else (
    echo ERROR: backend directory not found
)

echo.
echo Checking frontend directory...
if exist "frontend" (
    echo OK: frontend directory exists
    cd frontend
    if exist "package.json" (
        echo OK: package.json exists
    ) else (
        echo ERROR: package.json not found
    )
    cd ..
) else (
    echo ERROR: frontend directory not found
)

echo.
echo ========================================
echo Environment check completed
echo ========================================

:end
pause
