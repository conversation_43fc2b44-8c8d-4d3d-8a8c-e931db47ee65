@echo off
chcp 65001 >nul
echo ========================================
echo 🚀 量化交易监控系统启动脚本
echo ========================================
echo.

:: 设置颜色
color 0A

:: 检查Python是否安装
echo [1/6] 检查Python环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.8+并添加到系统PATH
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 检查Node.js是否安装
echo [2/6] 检查Node.js环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js未安装或未添加到PATH
    echo 请安装Node.js 16+并添加到系统PATH
    pause
    exit /b 1
)
echo ✅ Node.js环境检查通过

:: 安装后端依赖
echo [3/6] 安装后端依赖...
cd backend
if not exist "venv" (
    echo 创建Python虚拟环境...
    python -m venv venv
)
call venv\Scripts\activate.bat
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)
echo ✅ 后端依赖安装完成
cd ..

:: 安装前端依赖
echo [4/6] 安装前端依赖...
cd frontend
if not exist "node_modules" (
    echo 安装前端依赖包...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
)
echo ✅ 前端依赖安装完成
cd ..

:: 创建日志目录
echo [5/6] 创建必要目录...
if not exist "backend\logs" mkdir backend\logs
if not exist "data" mkdir data
echo ✅ 目录创建完成

:: 启动系统
echo [6/6] 启动系统服务...
echo.
echo 🔥 正在启动量化交易监控系统...
echo.
echo 📊 后端API: http://localhost:8000
echo 🌐 前端界面: http://localhost:3001
echo 📡 WebSocket: ws://localhost:8000/ws
echo.

:: 启动后端服务
echo 启动后端服务...
start "量化交易系统-后端" cmd /k "cd backend && venv\Scripts\activate.bat && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload"

:: 等待后端启动
echo 等待后端服务启动...
timeout /t 5 /nobreak >nul

:: 启动前端服务
echo 启动前端服务...
start "量化交易系统-前端" cmd /k "cd frontend && set PORT=3001 && npm start"

echo.
echo ========================================
echo ✅ 系统启动完成！
echo ========================================
echo.
echo 📝 使用说明:
echo 1. 后端API服务: http://localhost:8000
echo 2. 前端Web界面: http://localhost:3000
echo 3. API文档: http://localhost:8000/docs
echo 4. 系统健康检查: http://localhost:8000/health
echo.
echo 🔧 如需停止服务，请关闭对应的命令行窗口
echo.
echo 📚 更多信息请查看 README.md 文件
echo.
pause
